import { useState, useEffect } from 'react';
import { useParams, Navigate } from 'react-router-dom';
import { getArticleBySlug, getRelatedArticles } from '@/lib/blog-api';
import { BLOG_CATEGORIES, type BlogArticle } from '@/types/blog';
import { EnhancedNavbar } from '@/components/ui/enhanced-navbar';
import { Footer2 } from '@/components/ui/shadcnblocks-com-footer2';
import { Breadcrumbs } from '@/components/ui/breadcrumbs';
import { AuthorBio } from '@/components/ui/author-bio';
import { ReadNext } from '@/components/ui/read-next';
import { SEOHead } from '@/components/ui/seo-head';
import { Calendar, Clock, Tag, User } from 'lucide-react';

export default function BlogArticle() {
  const { slug } = useParams<{ slug: string }>();
  const [article, setArticle] = useState<BlogArticle | null>(null);
  const [relatedArticles, setRelatedArticles] = useState<BlogArticle[]>([]);
  const [loading, setLoading] = useState(true);
  const [notFound, setNotFound] = useState(false);

  useEffect(() => {
    const loadArticle = async () => {
      if (!slug) {
        setNotFound(true);
        setLoading(false);
        return;
      }

      try {
        const [articleData, related] = await Promise.all([
          getArticleBySlug(slug),
          getRelatedArticles(slug, 3)
        ]);

        if (!articleData) {
          setNotFound(true);
        } else {
          setArticle(articleData);
          setRelatedArticles(related);
        }
      } catch (error) {
        console.error('Error loading article:', error);
        setNotFound(true);
      } finally {
        setLoading(false);
      }
    };

    loadArticle();
  }, [slug]);

  if (loading) {
    return (
      <div className="min-h-screen bg-lungu">
        <EnhancedNavbar />
        <div className="pt-20 flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gango mx-auto mb-4"></div>
            <p className="text-libi/70 font-redhat">Loading article...</p>
          </div>
        </div>
      </div>
    );
  }

  if (notFound || !article) {
    return <Navigate to="/404" replace />;
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getCategoryName = (categoryId: string) => {
    const category = BLOG_CATEGORIES.find(cat => cat.id === categoryId);
    return category ? category.name : categoryId.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Create article schema for SEO
  const articleSchema = {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "headline": article.title,
    "description": article.description,
    "image": article.thumbnail,
    "author": {
      "@type": "Person",
      "name": article.author.name
    },
    "publisher": {
      "@type": "Organization",
      "name": "FunnelVision",
      "url": "https://funnelvision.com"
    },
    "datePublished": article.publishedAt,
    "dateModified": article.publishedAt,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://funnelvision.com/blog/${article.slug}`
    }
  };

  return (
    <div className="min-h-screen bg-lungu">
      <SEOHead
        title={`${article.title} | FunnelVision Blog`}
        description={article.description}
        jsonLd={articleSchema}
      />
      <EnhancedNavbar />
      
      <main className="pt-20">
        {/* Article Header */}
        <section className="py-12 px-4">
          <div className="max-w-4xl mx-auto">
            {/* Breadcrumbs */}
            <div className="mb-8">
              <Breadcrumbs
                items={[
                  { label: 'Blog', href: '/blog' },
                  { label: getCategoryName(article.category) },
                  { label: article.title }
                ]}
              />
            </div>

            {/* Category Badge */}
            <div className="mb-4">
              <span className="inline-block px-4 py-2 text-sm font-redhat font-medium bg-sulu text-bango rounded-full">
                {getCategoryName(article.category)}
              </span>
            </div>

            {/* Title */}
            <h1 className="font-redhat font-medium text-3xl md:text-4xl lg:text-5xl text-libi mb-6 leading-snug tracking-tight">
              {article.title}
            </h1>

            {/* Meta Information */}
            <div className="flex flex-wrap items-center gap-6 text-sm text-libi/70 mb-8">
              <div className="flex items-center gap-2">
                <User className="w-4 h-4" />
                <span className="font-redhat font-medium">{article.author.name}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>{formatDate(article.publishedAt)}</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                <span>{article.readingTime} min read</span>
              </div>
            </div>

            {/* Featured Image */}
            <div className="mb-12">
              <div className="relative overflow-hidden rounded-2xl aspect-video bg-white shadow-lg">
                <img
                  src={article.thumbnail}
                  alt={article.title}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.src = '/placeholder.svg';
                  }}
                />
              </div>
            </div>
          </div>
        </section>

        {/* Article Content */}
        <section className="pb-16 px-4">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-2xl p-8 md:p-12 shadow-sm border border-lungu">
              <div 
                className="prose prose-lg max-w-none prose-headings:font-redhat prose-headings:text-libi prose-p:text-libi/80 prose-p:leading-relaxed prose-a:text-gango prose-a:no-underline hover:prose-a:underline prose-strong:text-libi prose-ul:text-libi/80 prose-ol:text-libi/80"
                dangerouslySetInnerHTML={{ __html: article.content }}
              />

              {/* Tags */}
              {article.tags && article.tags.length > 0 && (
                <div className="mt-12 pt-8 border-t border-lungu">
                  <div className="flex items-center gap-2 mb-4">
                    <Tag className="w-4 h-4 text-libi/60" />
                    <span className="text-sm font-redhat font-medium text-libi/60">Tags:</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {article.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 text-xs font-redhat bg-lungu text-libi rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Author Bio */}
        <section className="pb-16 px-4">
          <div className="max-w-4xl mx-auto">
            <AuthorBio author={article.author} />
          </div>
        </section>

        {/* Related Articles */}
        {relatedArticles.length > 0 && (
          <ReadNext articles={relatedArticles} currentSlug={article.slug} />
        )}
      </main>

      <Footer2 />
    </div>
  );
}
