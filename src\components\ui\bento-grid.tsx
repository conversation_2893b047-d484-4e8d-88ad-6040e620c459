import { ReactNode } from "react";
import { ArrowRightIcon } from "@radix-ui/react-icons";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

const BentoGrid = ({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) => {
  return (
    <div
      className={cn(
        "grid w-full auto-rows-[22rem] grid-cols-3 gap-4",
        className,
      )}
    >
      {children}
    </div>
  );
};

const BentoCard = ({
  name,
  className,
  background,
  Icon,
  description,
  href,
  cta,
}: {
  name: string;
  className: string;
  background: ReactNode;
  Icon: React.ComponentType<{ className?: string }>;
  description: string;
  href: string;
  cta: string;
}) => (
  <div
    key={name}
    className={cn(
      "group relative col-span-3 flex flex-col justify-between overflow-hidden rounded-card min-h-[22rem]",
      // Updated to use #EDF0F2 background
      "bg-[#EDF0F2] [box-shadow:0_0_0_1px_rgba(0,0,0,.03),0_2px_4px_rgba(0,0,0,.05),0_12px_24px_rgba(0,0,0,.05)]",
      // dark styles
      "transform-gpu dark:bg-black dark:[border:1px_solid_rgba(255,255,255,.1)] dark:[box-shadow:0_-20px_80px_-20px_#ffffff1f_inset]",
      className,
    )}
  >
    <div>{background}</div>
    {/* Content section with proper spacing */}
    <div className="flex flex-col justify-between h-full p-6">
      <div className="flex flex-col gap-3">
        <Icon className="h-12 w-12 origin-left transform-gpu text-neutral-700 transition-all duration-300 ease-in-out group-hover:scale-75" />
        <h3 className="font-redhat font-medium leading-normal tracking-tight text-xl md:text-2xl text-neutral-700 dark:text-neutral-300">
          {name}
        </h3>
        <p className="font-redhat text-neutral-600 leading-relaxed">{description}</p>
      </div>

      {/* CTA Button at bottom */}
      <div className="mt-6 pt-4">
        <Button variant="default" asChild size="sm" className="cta-button pointer-events-auto">
          <a href={href}>
            {cta}
            <div className="cta-arrow border-2 border-white"></div>
          </a>
        </Button>
      </div>
    </div>
    <div className="pointer-events-none absolute inset-0 transform-gpu transition-all duration-300 group-hover:bg-black/[.03] group-hover:dark:bg-neutral-800/10" />
  </div>
);

export { BentoCard, BentoGrid };