import { BlogArticle } from '@/types/blog';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface BlogCardProps {
  article: BlogArticle;
  className?: string;
}

export function BlogCard({ article, className }: BlogCardProps) {
  const handleCardClick = () => {
    window.location.href = `/blog/${article.slug}`;
  };

  const handleReadMoreClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    window.location.href = `/blog/${article.slug}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div
      className={cn(
        "group cursor-pointer bg-transparent rounded-lg overflow-hidden transition-all duration-300",
        className
      )}
      onClick={handleCardClick}
    >
      {/* Thumbnail */}
      <div className="relative overflow-hidden aspect-video bg-lungu">
        <img
          src={article.thumbnail}
          alt={article.title}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          onError={(e) => {
            // Fallback to placeholder if image fails to load
            e.currentTarget.src = '/placeholder.svg';
          }}
        />
        
        {/* Hover overlay with Read More button */}
        <div className="absolute inset-0 bg-bango/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
          <Button
            size="sm"
            className="pointer-events-auto font-redhat font-medium bg-svibi hover:bg-svibi/90 text-white border-0 rounded-full"
            onClick={handleReadMoreClick}
          >
            Read more
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="p-3">
        {/* Title */}
        <h3 className="font-redhat font-medium text-sm md:text-base line-clamp-2 mb-2 group-hover:text-primary transition-colors">
          {article.title}
        </h3>

        {/* Date */}
        <div className="font-redhat text-xs text-muted-foreground">
          <span className="truncate">{formatDate(article.publishedAt)}</span>
        </div>
      </div>
    </div>
  );
}
