"use client";

import { Carousel } from "@/components/ui/carousel";


export function CarouselDemo() {
  // Define cards data for case studies with modal data (removed 4th card as requested)
  const caseStudyCardsData = [
    {
      title: "Cut wasted ad spend by 52.6%",
      button: "",
      src: "/images/case-studies/case-1.webp",
      logo: "/images/clients/10.svg",
      ctaLink: "/case-studies/boulies",
    },
    {
      title: "Increased monthly revenue 122%",
      button: "",
      src: "/images/case-studies/case-2.webp",
      logo: "/images/clients/14.png",
      ctaLink: "/case-studies/bunkie-life",
    },
    {
      title: "Increased revenue 8.4X in 12 months",
      button: "",
      src: "/images/case-studies/case-3.webp",
      logo: "/images/clients/13.svg",
      ctaLink: "/case-studies/kodiak",
    },
  ];

  return (
    <div className="relative overflow-hidden w-full h-full py-20">
      <Carousel slides={caseStudyCardsData} />
    </div>
  );
}