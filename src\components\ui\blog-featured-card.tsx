import { BlogArticle } from '@/types/blog';
import { Button } from '@/components/ui/button';
import { ArrowRight, Calendar, Clock, User } from 'lucide-react';

interface BlogFeaturedCardProps {
  article: BlogArticle;
}

export function BlogFeaturedCard({ article }: BlogFeaturedCardProps) {
  const handleClick = () => {
    window.location.href = `/blog/${article.slug}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="mb-16">
      <div className="bg-sulu rounded-3xl p-6 md:p-8 lg:p-12 mx-auto">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Content */}
          <div className="order-2 lg:order-1">
            {/* Featured Badge */}
            <div className="mb-4">
              <span className="inline-block px-4 py-2 text-sm font-redhat font-medium bg-bango rounded-full" style={{ color: '#F8FAFC' }}>
                Featured Article
              </span>
            </div>

            {/* Category */}
            <div className="mb-3">
              <span className="inline-block px-3 py-1 text-xs font-redhat font-medium bg-white text-bango rounded-full">
                {article.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </span>
            </div>

            {/* Title */}
            <h2 className="font-redhat font-medium text-2xl md:text-3xl lg:text-4xl text-libi mb-4 leading-snug tracking-tight">
              {article.title}
            </h2>

            {/* Description */}
            <p className="text-libi/80 text-base md:text-lg mb-6 leading-relaxed">
              {article.description}
            </p>

            {/* Meta Information */}
            <div className="flex items-center gap-6 text-sm text-libi/70 mb-8">
              <div className="flex items-center gap-2">
                <User className="w-4 h-4" />
                <span className="font-redhat font-medium">{article.author.name}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>{formatDate(article.publishedAt)}</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                <span>{article.readingTime} min read</span>
              </div>
            </div>

            {/* CTA Button */}
            <Button
              onClick={handleClick}
              className="font-redhat font-medium text-base md:text-lg tracking-tight gap-4 bg-svibi hover:bg-svibi/90 text-white border-0 rounded-full px-8 py-3"
            >
              Read Full Article
              <ArrowRight className="w-5 h-5" />
            </Button>
          </div>

          {/* Image */}
          <div className="order-1 lg:order-2">
            <div className="relative overflow-hidden rounded-2xl aspect-video bg-white shadow-lg">
              <img
                src={article.thumbnail}
                alt={article.title}
                className="w-full h-full object-cover"
                onError={(e) => {
                  // Fallback to placeholder if image fails to load
                  e.currentTarget.src = '/placeholder.svg';
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
