import { ChevronRight, Home } from 'lucide-react';
import { cn } from '@/lib/utils';

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
  className?: string;
}

export function Breadcrumbs({ items, className }: BreadcrumbsProps) {
  return (
    <nav className={cn("flex items-center space-x-2 text-sm", className)} aria-label="Breadcrumb">
      <a
        href="/"
        className="flex items-center text-libi/60 hover:text-gango transition-colors duration-200 font-redhat"
      >
        <Home className="w-4 h-4 mr-1" />
        Home
      </a>
      
      {items.map((item, index) => (
        <div key={index} className="flex items-center">
          <ChevronRight className="w-4 h-4 text-libi/40 mx-2" />
          {item.href && index < items.length - 1 ? (
            <a
              href={item.href}
              className="text-libi/60 hover:text-gango transition-colors duration-200 font-redhat"
            >
              {item.label}
            </a>
          ) : (
            <span className="text-libi font-redhat font-medium">
              {item.label}
            </span>
          )}
        </div>
      ))}
    </nav>
  );
}
