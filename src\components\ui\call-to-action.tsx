import { MoveR<PERSON>, Phone<PERSON><PERSON> } from "lucide-react";
import { <PERSON>ton, GetStartedButtonGreen } from "@/components/ui/button";

function CTA() {
  return (
    <div className="w-full py-20 lg:py-40">
      <div className="container mx-auto">
        <div className="bg-svibi rounded-3xl p-8 md:p-12 text-center">
          <div className="flex flex-col gap-2">
            <h3 className="font-redhat font-medium leading-snug tracking-tight text-2xl md:text-3xl lg:text-4xl text-white mb-8">
              Ready to Turn Intent <span className="text-gango">Into Revenue?</span>
            </h3>
            <p className="body-text text-white mb-8">
              No hard selling • No commitment
            </p>
          </div>
          <div className="flex flex-row gap-4 justify-center">
            <GetStartedButtonGreen onClick={() => window.location.href = '/book-a-call'}>
              Work with us
            </GetStartedButtonGreen>
          </div>
        </div>
      </div>
    </div>
  );
}

export { CTA };