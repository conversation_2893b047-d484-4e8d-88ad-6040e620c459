"use client";

import { useState } from "react";
import { ChevronRight } from "lucide-react";
import { Button, GetStartedButton } from "@/components/ui/button";

interface ComparisonItem {
  id: string;
  traditional: string;
  funnelvision: string;
  traditionalExpanded?: string;
  funnelvisionExpanded?: string;
}

const comparisonData: ComparisonItem[] = [
  {
    id: "metrics",
    traditional: "Focus on vanity metrics",
    funnelvision: "Laser-focused on revenue-driving KPIs",
    traditionalExpanded: "Traditional agencies focus on metrics like clicks, impressions, and traffic that may look impressive in reports but don't translate to actual sales or business growth.",
    funnelvisionExpanded: "We focus exclusively on KPIs that impact your bottom line - conversion rates, customer acquisition costs, lifetime value, and actual revenue generated from campaigns."
  },
  {
    id: "improvements",
    traditional: "Incremental improvements",
    funnelvision: "Transformative growth strategies",
    traditionalExpanded: "Most agencies make small, safe adjustments that deliver modest improvements over long periods.",
    funnelvisionExpanded: "We implement bold, data-driven strategies designed to create breakthrough growth and competitive advantages."
  },
  {
    id: "approaches",
    traditional: "Cookie-cutter approaches",
    funnelvision: "Custom, innovative solutions for each client",
    traditionalExpanded: "One-size-fits-all templates and strategies applied across different industries and business models.",
    funnelvisionExpanded: "Every strategy is built from the ground up based on your specific business model, market position, and growth objectives."
  },
  {
    id: "trends",
    traditional: "Reactive to market trends",
    funnelvision: "Proactive in creating new marketing angles",
    traditionalExpanded: "Following industry best practices and reacting to changes after competitors have already moved.",
    funnelvisionExpanded: "We anticipate market shifts and create innovative approaches that position you ahead of the competition."
  },
  {
    id: "integration",
    traditional: "Limited cross-channel integration",
    funnelvision: "Seamless multi-platform strategies",
    traditionalExpanded: "Siloed campaigns that don't work together cohesively across different marketing channels.",
    funnelvisionExpanded: "Integrated campaigns that amplify each other across all touchpoints for maximum impact and efficiency."
  },
  {
    id: "processes",
    traditional: "Manual, time-consuming processes",
    funnelvision: "AI-driven, scalable solutions",
    traditionalExpanded: "Heavy reliance on manual work and outdated processes that limit scalability and efficiency.",
    funnelvisionExpanded: "Cutting-edge automation and AI tools that scale with your business while maintaining personalized attention."
  }
];

export const ComparisonSection = () => {
  const [expandedItem, setExpandedItem] = useState<string | null>(null);

  const toggleExpanded = (id: string) => {
    setExpandedItem(expandedItem === id ? null : id);
  };

  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12 md:mb-16">
          <h2 className="h2-heading mb-4">
            <span className="text-svibi">Tradition Feels Comfortable, We Offer</span>{" "}
            <span className="text-gango">Growth</span>
          </h2>
        </div>

        {/* Header Bar */}
        <div className="flex items-stretch justify-center mb-12 max-w-4xl mx-auto">
          <div className="bg-bango px-8 py-4 rounded-l-lg font-redhat font-medium text-center flex-1 flex items-center justify-center" style={{ color: '#F8FAFC' }}>
            <span>Traditional<br />Approach</span>
          </div>
          <div className="bg-sulu text-black px-8 py-4 rounded-r-lg font-redhat font-medium flex items-center justify-center gap-2 flex-1">
            <div className="flex items-center gap-1">
              <div className="w-1 h-3 bg-black"></div>
              <div className="w-1 h-2 bg-black"></div>
              <div className="w-1 h-4 bg-black"></div>
              <div className="w-1 h-2 bg-black"></div>
            </div>
            <span className="font-redhat font-medium">funnelvision</span>
          </div>
        </div>

        {/* Comparison Items */}
        <div className="max-w-4xl mx-auto space-y-4">
          {comparisonData.map((item) => (
            <div
              key={item.id}
              className={`bg-white rounded-lg transition-all duration-300 cursor-pointer ${
                expandedItem === item.id
                  ? 'shadow-lg border-2 border-sulu'
                  : 'shadow-sm hover:shadow-md border border-gray-200'
              }`}
              onClick={() => toggleExpanded(item.id)}
              style={{ borderRadius: '8px' }}
            >
              <div className="grid grid-cols-[1fr_auto_1fr] gap-0 p-6 items-center">
                {/* Traditional Side */}
                <div className="flex items-center pr-4">
                  <p className="accordion-closed font-medium text-libi">{item.traditional}</p>
                </div>

                {/* Center Arrow */}
                <div className="flex items-center justify-center px-4">
                  <ChevronRight
                    className={`w-5 h-5 transition-all duration-300 ${
                      expandedItem === item.id
                        ? 'rotate-90 text-sulu'
                        : 'text-gray-400'
                    }`}
                  />
                </div>

                {/* FunnelVision Side */}
                <div className="flex items-center pl-4">
                  <p className="accordion-closed font-medium text-libi">{item.funnelvision}</p>
                </div>
              </div>

              {/* Expanded Content */}
              {expandedItem === item.id && (
                <div className="border-t border-sulu bg-white p-6" style={{ borderRadius: '0 0 8px 8px' }}>
                  <div className="grid grid-cols-[1fr_auto_1fr] gap-6 items-start">
                    <div>
                      <p className="font-redhat text-sm md:text-base text-libi leading-relaxed">
                        {item.traditionalExpanded}
                      </p>
                    </div>

                    {/* Center space - no line when opened */}
                    <div className="w-4"></div>

                    <div>
                      <p className="font-redhat font-medium text-sm md:text-base text-libi leading-relaxed">
                        {item.funnelvisionExpanded}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* CTA Button */}
        <div className="text-center mt-12">
          <GetStartedButton>
            Work with us
          </GetStartedButton>
        </div>
      </div>
    </section>
  );
};
