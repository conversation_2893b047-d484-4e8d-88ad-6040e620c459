import { EnhancedNavbar } from "@/components/ui/enhanced-navbar";
import { Footer2Demo } from "@/components/ui/footer2-demo";
import { SEOHead, createServiceSchema } from "@/components/ui/seo-head";
import { TestimonialsDemo } from "@/components/ui/testimonials-demo";
import { ContactForm } from "@/components/ui/contact-form";
import { But<PERSON> } from "@/components/ui/button";
import { MoveRight, FileText, Search, TrendingUp, Users, Target, BarChart3, Settings, CheckCircle, Edit } from "lucide-react";

const PageHeader = ({ title, subtitle }: { title: string; subtitle: string }) => (
  <section className="py-20 px-4">
    <div className="max-w-4xl mx-auto text-center">
      <h1 className="h1-heading text-foreground mb-6">
        {title}
      </h1>
      <p className="h1-description text-muted-foreground max-w-2xl mx-auto">
        {subtitle}
      </p>
    </div>
  </section>
);

const ImportanceSection = () => (
  <section className="py-20 px-4 bg-muted/30">
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold text-foreground mb-6">Importance of SEO Content</h2>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
          Google's algorithm rewards websites that regularly publish relevant, valuable content. More importantly, so do your customers—great content builds trust and guides them toward a purchase. Search isn't just about blue links on Google anymore—it's answering questions on voice assistants, appearing in AI-driven results, and showing up wherever your customers are looking.
        </p>
      </div>
    </div>
  </section>
);

const ContentServices = () => (
  <section className="py-20 px-4">
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold text-foreground mb-6">What We Do - Content Services</h2>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
          We create strategic content plans based on audience search data and intent, then produce high-quality content optimized for both search algorithms and human readers.
        </p>
      </div>
      <div className="grid md:grid-cols-2 gap-8">
        <div className="p-8 bg-white rounded-card border">
          <Target className="h-10 w-10 text-primary mb-6" />
          <h3 className="text-xl font-semibold mb-4">Content Strategy & Keyword Research</h3>
          <p className="text-muted-foreground">
            We begin by identifying what your target audience is searching for. We audit your existing content to see what's performing, what's missing, and analyze competitor content. Then we build a strategic content plan grounded in audience search data and intent.
          </p>
        </div>
        <div className="p-8 bg-white rounded-card border">
          <Edit className="h-10 w-10 text-primary mb-6" />
          <h3 className="text-xl font-semibold mb-4">High-Quality Content Creation</h3>
          <p className="text-muted-foreground">
            We produce content—from blog posts and articles to guides, infographics, and videos. Every piece is well-researched, on-brand, and provides real value. Our content is crafted to satisfy search algorithms and human readers—informative, compelling, and aligned with your brand voice.
          </p>
        </div>
        <div className="p-8 bg-white rounded-card border">
          <Settings className="h-10 w-10 text-primary mb-6" />
          <h3 className="text-xl font-semibold mb-4">On-Page SEO Optimization</h3>
          <p className="text-muted-foreground">
            Each piece of content is fully optimized. We handle meta titles/descriptions, internal linking, image alt tags, schema markup, and all on-page SEO best practices, so your content has the best chance to rank #1.
          </p>
        </div>
        <div className="p-8 bg-white rounded-card border">
          <BarChart3 className="h-10 w-10 text-primary mb-6" />
          <h3 className="text-xl font-semibold mb-4">Multi-Channel Content Distribution</h3>
          <p className="text-muted-foreground">
            We amplify your content through social media, email newsletters, and content repurposing—turning a blog post into a video summary, infographic, or social snippets to extend its reach and maximize ROI on each piece of content.
          </p>
        </div>
      </div>
    </div>
  </section>
);

const ApproachSection = () => (
  <section className="py-20 px-4 bg-muted/30">
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold text-foreground mb-6">Our Approach - Marrying SEO with Content Creativity</h2>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
          We don't create content for content's sake. Every piece is guided by data (what your audience wants and what keywords will drive them) but crafted with creativity to stand out. Effective SEO is about expanding your digital footprint across the entire online ecosystem, and it starts with chasing consumers, not algorithms.
        </p>
      </div>
      <div className="grid md:grid-cols-2 gap-8">
        <div className="bg-white rounded-card p-8 border">
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <Users className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-semibold">User-First Content Strategy</h3>
          </div>
          <p className="text-muted-foreground">
            We focus on what the user is truly looking for (search intent) rather than trying to game algorithms. This user-first mindset in content naturally pleases both user and algorithm, creating content that genuinely serves your audience's needs.
          </p>
        </div>

        <div className="bg-white rounded-card p-8 border">
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <TrendingUp className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-semibold">Multi-Channel Content Approach</h3>
          </div>
          <p className="text-muted-foreground">
            From optimizing for Google's featured snippets and AI-driven search results to leveraging user-generated content and social search (optimizing for TikTok and YouTube searches), we ensure your content strategy keeps your brand everywhere your customers are searching.
          </p>
        </div>

        <div className="bg-white rounded-card p-8 border">
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <FileText className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-semibold">Content Update & Maintenance</h3>
          </div>
          <p className="text-muted-foreground">
            We don't set and forget content. We monitor content performance and refresh or expand key articles to keep them ranking and traffic growing. Google rewards up-to-date content, so this ongoing maintenance is a crucial value-add.
          </p>
        </div>

        <div className="bg-white rounded-card p-8 border">
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <BarChart3 className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-semibold">Performance-Driven Results</h3>
          </div>
          <p className="text-muted-foreground">
            Every content piece is created with clear goals and measurable outcomes. We track rankings, organic traffic, and conversions to ensure your content marketing delivers real business results, not just vanity metrics.
          </p>
        </div>
      </div>
    </div>
  </section>
);





const SocialProofSection = () => (
  <section className="py-20 px-4">
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold text-foreground mb-6">Content That Drives Organic Growth</h2>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
          Our strategic content approach consistently delivers measurable improvements in organic traffic and search rankings.
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-8 mb-16">
        <div className="text-center p-8 bg-white rounded-card border">
          <div className="text-4xl font-bold text-primary mb-2">300%</div>
          <div className="text-lg font-semibold mb-2">Organic Traffic Increase</div>
          <p className="text-muted-foreground text-sm">Average growth within 12 months of content strategy</p>
        </div>
        <div className="text-center p-8 bg-white rounded-card border">
          <div className="text-4xl font-bold text-primary mb-2">85%</div>
          <div className="text-lg font-semibold mb-2">First Page Rankings</div>
          <p className="text-muted-foreground text-sm">Of targeted keywords ranking on Google's first page</p>
        </div>
        <div className="text-center p-8 bg-white rounded-card border">
          <div className="text-4xl font-bold text-primary mb-2">40%</div>
          <div className="text-lg font-semibold mb-2">Lead Generation Boost</div>
          <p className="text-muted-foreground text-sm">From organic search through strategic content</p>
        </div>
      </div>

      <div className="bg-primary/5 rounded-card p-8 border border-primary/20">
        <div className="flex items-start gap-4">
          <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
            <CheckCircle className="w-6 h-6 text-white" />
          </div>
          <div>
            <blockquote className="text-lg font-medium text-foreground mb-2">
              "FunnelVision's content strategy tripled our organic traffic in under a year. Their approach goes beyond basic SEO—
              they create content that actually converts visitors into customers."
            </blockquote>
            <cite className="text-muted-foreground">— Content Marketing Manager, B2B SaaS</cite>
          </div>
        </div>
      </div>
    </div>
  </section>
);

const SeoContent = () => {
  const serviceSchema = createServiceSchema(
    "SEO & Content Engineering",
    "Strategic content creation and SEO optimization that drives organic growth. We create content that ranks high in search results and converts visitors into customers."
  );

  return (
    <div className="min-h-screen bg-background">
      <SEOHead
        title="SEO & Content Engineering | FunnelVision Services"
        description="Strategic content creation and SEO optimization that drives organic growth. We create content that ranks high in search results and converts visitors into customers through data-driven content strategies."
        jsonLd={serviceSchema}
      />
      <EnhancedNavbar />
      <div className="pt-16">
        <PageHeader
          title="Strategic Content Creation & SEO that Drives Organic Growth"
          subtitle="We create content that doesn't just rank—it converts. Our approach combines data-driven SEO with compelling storytelling to attract your ideal customers and guide them toward a purchase."
        />
        <ImportanceSection />
        <ContentServices />
        <ApproachSection />
        <SocialProofSection />
        <ContactForm
          title="Ready to Dominate Search Results?"
          subtitle="Stop creating content that gets lost in the noise. Let us build a content strategy that attracts your ideal customers and drives real business growth. Contact us for a free content audit."
        />
        <TestimonialsDemo />
      </div>
      <Footer2Demo />
    </div>
  );
};

export default SeoContent;
