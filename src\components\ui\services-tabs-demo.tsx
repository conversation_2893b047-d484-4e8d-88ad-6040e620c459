import { 
  Search, 
  Rocket, 
  Eye, 
  Code, 
  FileText, 
  BarChart3 
} from "lucide-react";

import { Feature108 } from "@/components/ui/feature108";

const servicesData = {
  badge: "",
  heading: "Search & AI-Marketing That Turns Intent Into Revenue",
  description: "Discover what we offer to grow your business",
  tabs: [
    {
      value: "ai-discovery",
      icon: <Search className="h-auto w-4 shrink-0" />,
      label: "AI Discovery",
      content: {
        badge: "Advanced AI",
        title: "Paid Search Orchestration",
        description:
          "Strategic paid search campaigns that turn customer intent into profitable conversions at scale.",
        buttonText: "Learn More",
        buttonHref: "/services/ai-discovery",
        imageSrc: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=600&fit=crop&crop=center",
        imageAlt: "AI Discovery and Search Optimization",
      },
    },
    {
      value: "paid-search",
      icon: <Rocket className="h-auto w-4 shrink-0" />,
      label: "Paid Search", // label matches source, content title keeps full phrasing
      content: {
        badge: "Strategic Campaigns",
        title: "Paid Search Orchestration",
        description:
          "Strategic paid search campaigns that turn customer intent into profitable conversions at scale.",
        buttonText: "Learn More",
        buttonHref: "/services/paid-search",
        imageSrc: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop&crop=center",
        imageAlt: "Paid Search Campaign Management",
      },
    },
    {
      value: "cro-ux",
      icon: <Eye className="h-auto w-4 shrink-0" />,
      label: "CRO and UX",
      content: {
        badge: "Optimization Cycles",
        title: "Paid Search Orchestration",
        description:
          "Strategic paid search campaigns that turn customer intent into profitable conversions at scale.",
        buttonText: "Learn More",
        buttonHref: "/services/cro-ux",
        imageSrc: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop&crop=center",
        imageAlt: "Conversion Rate Optimization",
      },
    },
    {
      value: "technical-seo",
      icon: <Code className="h-auto w-4 shrink-0" />,
      label: "Technical SEO",
      content: {
        badge: "Technical Excellence",
        title: "Paid Search Orchestration",
        description:
          "Strategic paid search campaigns that turn customer intent into profitable conversions at scale.",
        buttonText: "Learn More",
        buttonHref: "/services/technical-seo",
        imageSrc: "https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800&h=600&fit=crop&crop=center",
        imageAlt: "Technical SEO Services",
      },
    },
    {
      value: "web-development",
      icon: <Code className="h-auto w-4 shrink-0" />,
      label: "Web Development",
      content: {
        badge: "High Performance",
        title: "Paid Search Orchestration",
        description:
          "Strategic paid search campaigns that turn customer intent into profitable conversions at scale.",
        buttonText: "Learn More",
        buttonHref: "/services/web-development",
        imageSrc: "https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800&h=600&fit=crop&crop=center",
        imageAlt: "Web Development Services",
      },
    },
  ],
};

function ServicesTabsDemo() {
  return <Feature108 {...servicesData} />;
}

export { ServicesTabsDemo };
