import { useState, useEffect } from 'react';
import { BlogCategory } from '@/types/blog';
import { getAllArticles, getArticlesByCategory, getFeaturedArticle } from '@/lib/blog-api';
import { BlogFeaturedCard } from '@/components/ui/blog-featured-card';
import { BlogCard } from '@/components/ui/blog-card';
import { BlogCategoryTabs } from '@/components/ui/blog-category-tabs';
import { EnhancedNavbar } from '@/components/ui/enhanced-navbar';
import { Footer2 } from '@/components/ui/shadcnblocks-com-footer2';
import { SEOHead } from '@/components/ui/seo-head';
import type { BlogArticle } from '@/types/blog';

export default function Blog() {
  const [activeCategory, setActiveCategory] = useState<'all' | BlogCategory>('all');
  const [articles, setArticles] = useState<BlogArticle[]>([]);
  const [featuredArticle, setFeaturedArticle] = useState<BlogArticle | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadArticles = async () => {
      setLoading(true);
      try {
        const [featured, allArticles] = await Promise.all([
          getFeaturedArticle(),
          activeCategory === 'all' ? getAllArticles() : getArticlesByCategory(activeCategory)
        ]);

        setFeaturedArticle(featured);
        setArticles(allArticles);
      } catch (error) {
        console.error('Error loading articles:', error);
      } finally {
        setLoading(false);
      }
    };

    loadArticles();
  }, [activeCategory]);

  // Filter out featured article from regular articles to avoid duplication
  const regularArticles = articles.filter(article => 
    !featuredArticle || article.id !== featuredArticle.id
  );

  // Create blog schema for SEO
  const blogSchema = {
    "@context": "https://schema.org",
    "@type": "Blog",
    "name": "FunnelVision Blog",
    "description": "Expert insights on search marketing, AI discovery, conversion optimization, and growth strategies for DTC eCommerce brands.",
    "url": "https://funnelvision.com/blog",
    "publisher": {
      "@type": "Organization",
      "name": "FunnelVision",
      "url": "https://funnelvision.com"
    },
    "blogPost": articles.map(article => ({
      "@type": "BlogPosting",
      "headline": article.title,
      "description": article.description,
      "url": `https://funnelvision.com/blog/${article.slug}`,
      "datePublished": article.publishedAt,
      "author": {
        "@type": "Person",
        "name": article.author.name
      }
    }))
  };

  return (
    <div className="min-h-screen bg-lungu">
      <SEOHead
        title="FunnelVision Blog - Expert Marketing Insights & Strategies"
        description="Expert insights on conversion optimization, digital marketing, and growth strategies to help you scale your business. Latest articles on AI search, CRO, Google Ads, and more."
        jsonLd={blogSchema}
      />
      <EnhancedNavbar />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h1 className="font-redhat font-medium text-4xl md:text-5xl lg:text-6xl text-libi mb-6 leading-snug tracking-tight">
                FunnelVision <span className="text-gango">Blog</span>
              </h1>
              <p className="text-lg md:text-xl text-libi/70 max-w-3xl mx-auto leading-relaxed">
                Expert insights on conversion optimization, digital marketing, and growth strategies 
                to help you scale your business.
              </p>
            </div>

            {/* Featured Article */}
            {featuredArticle && (
              <BlogFeaturedCard article={featuredArticle} />
            )}

            {/* Category Tabs */}
            <BlogCategoryTabs
              activeCategory={activeCategory}
              onCategoryChange={setActiveCategory}
            />

            {/* Articles Grid */}
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {Array.from({ length: 6 }).map((_, i) => (
                  <div key={i} className="space-y-3">
                    <div className="bg-white rounded-lg aspect-video animate-pulse border border-lungu" />
                    <div className="h-4 bg-white rounded animate-pulse border border-lungu" />
                    <div className="h-3 bg-white rounded w-2/3 animate-pulse border border-lungu" />
                  </div>
                ))}
              </div>
            ) : regularArticles.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {regularArticles.map((article) => (
                  <BlogCard key={article.id} article={article} />
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <p className="text-lg text-libi/70 font-redhat">
                  No articles found in this category yet.
                </p>
              </div>
            )}
          </div>
        </section>
      </main>

      <Footer2 />
    </div>
  );
}
