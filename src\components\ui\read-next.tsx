import { BlogArticle } from '@/types/blog';
import { BlogCard } from '@/components/ui/blog-card';

interface ReadNextProps {
  articles: BlogArticle[];
  currentSlug: string;
}

export function ReadNext({ articles, currentSlug }: ReadNextProps) {
  // Filter out the current article and limit to 3 articles
  const relatedArticles = articles
    .filter(article => article.slug !== currentSlug)
    .slice(0, 3);

  if (relatedArticles.length === 0) {
    return null;
  }

  return (
    <section className="py-16">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="h2-heading text-libi mb-4">
            Read This <span className="text-gango">Next</span>
          </h2>
          <p className="body-text text-libi/70 max-w-2xl mx-auto">
            Continue exploring our latest insights and strategies
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {relatedArticles.map((article) => (
            <BlogCard key={article.id} article={article} />
          ))}
        </div>
      </div>
    </section>
  );
}
