import React from "react";
import { Accordion, Accordion<PERSON>ontent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Plus } from "lucide-react";

const faqData = [
  {
    id: "1",
    question: "What is FunnelVision?",
    answer: "FunnelVision is a specialized marketing agency that helps businesses turn customer intent into profitable growth through strategic paid search, AI discovery, conversion optimization, and web development services.",
  },
  {
    id: "2",
    question: "How does FunnelVision work?",
    answer: "We follow our proven 5-step process: Discover (capture demand), Convert (optimize funnels), Loop (track and improve), Scale (expand successful campaigns), and Capture (maximize conversions). This creates a continuous cycle of growth and optimization.",
  },
  {
    id: "3",
    question: "Who are the ideal clients for FunnelVision?",
    answer: "We work best with e-commerce businesses, SaaS companies, and service providers who are ready to scale their marketing efforts and have a budget of at least $10k/month for advertising spend.",
  },
  {
    id: "4",
    question: "Do you require long-term contracts?",
    answer: "We offer flexible engagement options. While we recommend at least 3-6 months to see meaningful results, we don't lock you into lengthy contracts. Our goal is to deliver value that makes you want to continue working with us.",
  },
  {
    id: "5",
    question: "What results can I expect?",
    answer: "Results vary by business, but our clients typically see 20-40% improvements in conversion rates, 30-60% increases in qualified leads, and 2-5x return on ad spend within the first 90 days.",
  },
  {
    id: "6",
    question: "How do you measure success?",
    answer: "We focus on metrics that matter to your bottom line: cost per acquisition, lifetime value, return on ad spend, conversion rates, and overall revenue growth. We provide detailed reporting and regular strategy sessions.",
  },
  {
    id: "7",
    question: "Do you work with businesses in all industries?",
    answer: "While we have experience across many industries, we specialize in e-commerce, SaaS, professional services, and businesses with clear conversion funnels. We're selective about partnerships to ensure we can deliver exceptional results.",
  },
];

function FAQDemo() {
  return (
    <div className="w-full py-20 lg:py-40 bg-background-alt">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="h2-heading mb-4">
            <span className="text-svibi">Got Questions?</span>{" "}
            <span className="text-gango">We've Got Answers.</span>
          </h2>
        </div>

        <div className="max-w-4xl mx-auto">
          <Accordion type="single" collapsible className="space-y-0">
            {faqData.map((item) => (
              <AccordionItem
                key={item.id}
                value={item.id}
                className="border-b border-[#9ca3af] last:border-b-0"
              >
                <AccordionTrigger
                  hideIcon={true}
                  className="flex items-center justify-between py-6 px-0 text-left hover:no-underline group"
                >
                  <span className="accordion-closed font-medium text-svibi pr-4">
                    {item.question}
                  </span>
                  <div className="w-6 h-6 bg-libi rounded-full flex items-center justify-center shrink-0">
                    <Plus className="h-4 w-4 text-white transition-transform duration-200 group-data-[state=open]:rotate-45" />
                  </div>
                </AccordionTrigger>
                <AccordionContent className="font-redhat text-base md:text-lg pb-6 pt-0 text-libi leading-relaxed">
                  {item.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </div>
  );
}

export { FAQDemo };