import { useEffect, useMemo, useState } from "react";
import { motion } from "framer-motion";
import { Search, BarChart3, Zap, Star } from "lucide-react";
import { Button, GetStartedButton } from "@/components/ui/button";
import { LogoCarousel } from "@/components/ui/logo-carousel";
import { sampleLogos } from "@/components/ui/sample-logos";
import { useIsMobile } from "@/hooks/use-mobile";

function Hero() {
  const isMobile = useIsMobile();
  const [isTablet, setIsTablet] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  const getColumnCount = () => {
    if (isMobile) return 3;
    if (isTablet) return 4;
    return 6;
  };

  return (
    <div className="w-full bg-lungu">
      <div className="container mx-auto">
        <div className="flex gap-8 py-20 lg:py-40 items-center justify-center flex-col">
          {/* G2 Rating Badge */}
          <div className="flex items-center gap-2">
            <div className="flex">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-4 h-4 fill-yellow-500 text-yellow-500" />
              ))}
            </div>
            <span className="font-redhat text-sm text-libi/70">G2 Verified 5-Star Rating</span>
          </div>

          <div className="flex gap-6 flex-col items-center">
            <h1 className="h1-heading max-w-4xl text-center">
              <span className="text-libi">Win </span>
              <span className="text-gango">Search.</span>
              <br />
              <span className="text-libi">Win </span>
              <span className="text-gango">Conversion.</span>
            </h1>

            <div className="text-center max-w-2xl">
              <p className="h1-description text-libi">
                Own results across AI answers, Paid Search, and SEO. Turn visibility into profit with CRO and UX that scale.
              </p>
            </div>
          </div>
          <div className="flex flex-col md:flex-row items-center gap-3 md:gap-4">
            <GetStartedButton onClick={() => window.location.href = '/book-a-call'}>
              Work with us
            </GetStartedButton>
            <p className="font-redhat text-sm text-libi/60">
              Request an audit
            </p>
          </div>

          {/* Logo Carousel - moved from services section */}
          <div className="flex flex-col items-center gap-4 mt-16 mb-8">
            <div className="bg-transparent flex items-center justify-center px-8 py-6">
              <LogoCarousel
                logos={sampleLogos}
                columnCount={getColumnCount()}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export { Hero };