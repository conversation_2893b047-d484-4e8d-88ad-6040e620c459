import { BlogArticle, BlogCategory } from '@/types/blog';

// Static blog data for the new blog system
const SAMPLE_ARTICLES: BlogArticle[] = [
  {
    id: '1',
    slug: 'ai-search-optimization-2024',
    title: 'AI Search Optimization: The Future of Discovery Marketing',
    description: 'Discover how AI-powered search is revolutionizing how customers find products and services online, and learn strategies to optimize for this new landscape.',
    content: `
      <h2>The Rise of AI-Powered Search</h2>
      <p>Artificial Intelligence is fundamentally changing how search engines understand and respond to user queries. From Google's RankBrain to ChatGPT's integration into Bing, AI is making search more conversational and contextual than ever before.</p>

      <h3>Key Changes in AI Search</h3>
      <ul>
        <li>Natural language processing improvements</li>
        <li>Contextual understanding of user intent</li>
        <li>Personalized search results</li>
        <li>Voice search optimization</li>
      </ul>

      <h3>Optimizing for AI Search</h3>
      <p>To succeed in this new landscape, businesses need to focus on creating content that answers specific questions and provides comprehensive value to users.</p>

      <h4>Best Practices:</h4>
      <ol>
        <li>Create comprehensive, topic-focused content</li>
        <li>Use natural language and conversational tone</li>
        <li>Implement structured data markup</li>
        <li>Focus on user intent rather than just keywords</li>
      </ol>

      <p>The future of search is here, and businesses that adapt to AI-powered discovery will have a significant competitive advantage.</p>
    `,
    thumbnail: '/images/blog/ai-search-optimization.jpg',
    publishedAt: '2024-08-15T10:00:00Z',
    readingTime: 5,
    category: 'ai-search',
    author: {
      name: 'Yana Naidenova',
      avatar: '/images/team/yana-avatar.jpg',
      bio: 'Yana is the founder of FunnelVision and a leading expert in AI-powered marketing strategies. With over 8 years of experience in digital marketing, she helps businesses leverage cutting-edge technology to drive growth.',
      linkedinUrl: 'https://linkedin.com/in/yananaidenova'
    },
    tags: ['AI', 'Search Optimization', 'Digital Marketing', 'SEO'],
    featured: true
  },
  {
    id: '2',
    slug: 'conversion-rate-optimization-guide',
    title: 'The Complete Guide to Conversion Rate Optimization',
    description: 'Learn proven strategies to increase your website conversion rates and turn more visitors into customers with data-driven CRO techniques.',
    content: `
      <h2>Understanding Conversion Rate Optimization</h2>
      <p>Conversion Rate Optimization (CRO) is the systematic process of increasing the percentage of website visitors who complete a desired action, whether that's making a purchase, signing up for a newsletter, or filling out a contact form.</p>

      <h3>Why CRO Matters</h3>
      <p>Even small improvements in conversion rates can have massive impacts on your bottom line. A 1% increase in conversion rate can translate to thousands of dollars in additional revenue.</p>

      <h3>Key CRO Strategies</h3>
      <ul>
        <li>A/B testing different page elements</li>
        <li>Optimizing page load speeds</li>
        <li>Improving mobile user experience</li>
        <li>Streamlining checkout processes</li>
        <li>Using social proof and testimonials</li>
      </ul>

      <h3>Getting Started with CRO</h3>
      <p>Begin by analyzing your current conversion funnel to identify drop-off points. Use tools like Google Analytics and heatmap software to understand user behavior.</p>

      <p>Remember: CRO is an ongoing process, not a one-time fix. Continuous testing and optimization will yield the best results.</p>
    `,
    thumbnail: '/images/blog/cro-guide.jpg',
    publishedAt: '2024-08-12T14:30:00Z',
    readingTime: 7,
    category: 'conversion-rate-optimization',
    author: {
      name: 'Yana Naidenova',
      avatar: '/images/team/yana-avatar.jpg',
      bio: 'Yana is the founder of FunnelVision and a leading expert in AI-powered marketing strategies. With over 8 years of experience in digital marketing, she helps businesses leverage cutting-edge technology to drive growth.',
      linkedinUrl: 'https://linkedin.com/in/yananaidenova'
    },
    tags: ['CRO', 'Conversion Optimization', 'Website Optimization', 'A/B Testing']
  },
  {
    id: '3',
    slug: 'google-ads-optimization-strategies',
    title: 'Google Ads Optimization: Advanced Strategies for 2024',
    description: 'Master Google Ads with advanced optimization techniques that drive better ROI and lower cost-per-acquisition for your campaigns.',
    content: `
      <h2>The Evolution of Google Ads</h2>
      <p>Google Ads continues to evolve with machine learning and automation taking center stage. Understanding how to leverage these tools while maintaining control over your campaigns is crucial for success.</p>

      <h3>Key Optimization Areas</h3>
      <ul>
        <li>Smart Bidding strategies and automation</li>
        <li>Audience targeting and segmentation</li>
        <li>Ad copy testing and optimization</li>
        <li>Landing page experience improvements</li>
        <li>Negative keyword management</li>
      </ul>

      <h3>Advanced Techniques</h3>
      <p>Beyond basic optimization, advanced practitioners focus on attribution modeling, cross-channel insights, and predictive analytics to drive superior performance.</p>

      <h4>Performance Max Campaigns</h4>
      <p>Google's Performance Max campaigns represent the future of automated advertising. Learn how to set them up for success while maintaining strategic control.</p>

      <p>The key to Google Ads success in 2024 is finding the right balance between automation and human insight.</p>
    `,
    thumbnail: '/images/blog/google-ads-optimization.jpg',
    publishedAt: '2024-08-10T09:15:00Z',
    readingTime: 6,
    category: 'google-ads',
    author: {
      name: 'Yana Naidenova',
      avatar: '/images/team/yana-avatar.jpg',
      bio: 'Yana is the founder of FunnelVision and a leading expert in AI-powered marketing strategies. With over 8 years of experience in digital marketing, she helps businesses leverage cutting-edge technology to drive growth.',
      linkedinUrl: 'https://linkedin.com/in/yananaidenova'
    },
    tags: ['Google Ads', 'PPC', 'Paid Search', 'Campaign Optimization']
  },
  {
    id: '4',
    slug: 'youtube-ads-creative-strategies',
    title: 'YouTube Ads: Creative Strategies That Convert',
    description: 'Discover how to create compelling YouTube ad campaigns that capture attention, engage viewers, and drive conversions at scale.',
    content: `
      <h2>The Power of Video Advertising</h2>
      <p>YouTube has become the second largest search engine in the world, making it an essential platform for reaching your target audience through video content.</p>

      <h3>Types of YouTube Ads</h3>
      <ul>
        <li>Skippable in-stream ads (TrueView)</li>
        <li>Non-skippable in-stream ads</li>
        <li>Video discovery ads</li>
        <li>Bumper ads (6-second non-skippable)</li>
        <li>YouTube Shorts ads</li>
      </ul>

      <h3>Creative Best Practices</h3>
      <p>The first 5 seconds of your YouTube ad are crucial. Hook viewers immediately with compelling visuals, clear value propositions, and emotional triggers.</p>

      <h4>Storytelling Framework</h4>
      <ol>
        <li>Hook: Grab attention in the first 3 seconds</li>
        <li>Problem: Identify the viewer's pain point</li>
        <li>Solution: Present your product/service as the answer</li>
        <li>Proof: Show social proof or results</li>
        <li>Call-to-Action: Clear next step</li>
      </ol>

      <p>Remember: YouTube ads should feel native to the platform while clearly communicating your brand message.</p>
    `,
    thumbnail: '/images/blog/youtube-ads-creative.jpg',
    publishedAt: '2024-08-08T16:45:00Z',
    readingTime: 5,
    category: 'youtube-ads',
    author: {
      name: 'Yana Naidenova',
      avatar: '/images/team/yana-avatar.jpg',
      bio: 'Yana is the founder of FunnelVision and a leading expert in AI-powered marketing strategies. With over 8 years of experience in digital marketing, she helps businesses leverage cutting-edge technology to drive growth.',
      linkedinUrl: 'https://linkedin.com/in/yananaidenova'
    },
    tags: ['YouTube Ads', 'Video Marketing', 'Creative Strategy', 'Video Advertising']
  },
  {
    id: '5',
    slug: 'seo-content-strategy-2024',
    title: 'SEO Content Strategy: Creating Content That Ranks and Converts',
    description: 'Learn how to develop an SEO content strategy that not only ranks well in search engines but also drives meaningful conversions for your business.',
    content: `
      <h2>The Modern SEO Landscape</h2>
      <p>SEO has evolved far beyond keyword stuffing and link building. Today's successful SEO strategies focus on user intent, content quality, and comprehensive topic coverage.</p>

      <h3>Content Strategy Fundamentals</h3>
      <ul>
        <li>Keyword research and intent mapping</li>
        <li>Topic clustering and content pillars</li>
        <li>E-A-T (Expertise, Authoritativeness, Trustworthiness)</li>
        <li>Technical SEO optimization</li>
        <li>User experience signals</li>
      </ul>

      <h3>Creating Content That Ranks</h3>
      <p>Focus on creating comprehensive, authoritative content that thoroughly addresses user queries. Google's algorithms increasingly favor content that demonstrates expertise and provides genuine value.</p>

      <h4>Content Optimization Checklist</h4>
      <ol>
        <li>Target primary and secondary keywords naturally</li>
        <li>Optimize title tags and meta descriptions</li>
        <li>Use header tags (H1, H2, H3) strategically</li>
        <li>Include relevant internal and external links</li>
        <li>Optimize images with alt text</li>
        <li>Ensure fast page load speeds</li>
      </ol>

      <p>Remember: The best SEO strategy is creating genuinely helpful content for your target audience.</p>
    `,
    thumbnail: '/images/blog/seo-content-strategy.jpg',
    publishedAt: '2024-08-06T11:30:00Z',
    readingTime: 7,
    category: 'seo',
    author: {
      name: 'Yana Naidenova',
      avatar: '/images/team/yana-avatar.jpg',
      bio: 'Yana is the founder of FunnelVision and a leading expert in AI-powered marketing strategies. With over 8 years of experience in digital marketing, she helps businesses leverage cutting-edge technology to drive growth.',
      linkedinUrl: 'https://linkedin.com/in/yananaidenova'
    },
    tags: ['SEO', 'Content Strategy', 'Search Engine Optimization', 'Content Marketing']
  },
  {
    id: '6',
    slug: 'marketing-strategy-framework-2024',
    title: 'Building a Winning Marketing Strategy Framework for Growth',
    description: 'Develop a comprehensive marketing strategy that aligns with your business goals and drives sustainable growth across all channels.',
    content: `
      <h2>The Foundation of Marketing Success</h2>
      <p>A solid marketing strategy serves as the blueprint for all your marketing efforts. It ensures consistency, maximizes ROI, and provides clear direction for your team.</p>

      <h3>Strategic Framework Components</h3>
      <ul>
        <li>Market research and competitive analysis</li>
        <li>Target audience definition and personas</li>
        <li>Value proposition and positioning</li>
        <li>Channel strategy and mix</li>
        <li>Budget allocation and resource planning</li>
        <li>KPIs and measurement framework</li>
      </ul>

      <h3>Multi-Channel Integration</h3>
      <p>Modern marketing success requires seamless integration across all touchpoints. Your strategy should create a cohesive customer journey from awareness to advocacy.</p>

      <h4>Channel Prioritization Matrix</h4>
      <p>Evaluate channels based on:</p>
      <ol>
        <li>Audience reach and relevance</li>
        <li>Cost-effectiveness and ROI potential</li>
        <li>Resource requirements and capabilities</li>
        <li>Competitive landscape and opportunities</li>
      </ol>

      <h3>Measurement and Optimization</h3>
      <p>Implement robust tracking and analytics to measure performance against your strategic objectives. Regular review and optimization ensure your strategy remains effective and relevant.</p>

      <p>A well-executed marketing strategy is the difference between random acts of marketing and systematic business growth.</p>
    `,
    thumbnail: '/images/blog/marketing-strategy-framework.jpg',
    publishedAt: '2024-08-04T13:20:00Z',
    readingTime: 8,
    category: 'marketing-strategy',
    author: {
      name: 'Yana Naidenova',
      avatar: '/images/team/yana-avatar.jpg',
      bio: 'Yana is the founder of FunnelVision and a leading expert in AI-powered marketing strategies. With over 8 years of experience in digital marketing, she helps businesses leverage cutting-edge technology to drive growth.',
      linkedinUrl: 'https://linkedin.com/in/yananaidenova'
    },
    tags: ['Marketing Strategy', 'Growth Marketing', 'Strategic Planning', 'Business Growth']
  }
];

/**
 * Get all blog articles sorted by publish date (newest first)
 */
export async function getAllArticles(): Promise<BlogArticle[]> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));

  return SAMPLE_ARTICLES.sort((a, b) =>
    new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
  );
}

/**
 * Get articles by category
 */
export async function getArticlesByCategory(category: BlogCategory): Promise<BlogArticle[]> {
  const allArticles = await getAllArticles();
  return allArticles.filter(article => article.category === category);
}

/**
 * Get featured article (most recent featured article)
 */
export async function getFeaturedArticle(): Promise<BlogArticle | null> {
  const allArticles = await getAllArticles();
  const featuredArticles = allArticles.filter(article => article.featured);
  return featuredArticles.length > 0 ? featuredArticles[0] : null;
}

/**
 * Get article by slug
 */
export async function getArticleBySlug(slug: string): Promise<BlogArticle | null> {
  const allArticles = await getAllArticles();
  return allArticles.find(article => article.slug === slug) || null;
}

/**
 * Get recent articles (for homepage)
 */
export async function getRecentArticles(limit: number = 4): Promise<BlogArticle[]> {
  const allArticles = await getAllArticles();
  return allArticles.slice(0, limit);
}

/**
 * Get related articles (same category, excluding current article)
 */
export async function getRelatedArticles(currentSlug: string, limit: number = 3): Promise<BlogArticle[]> {
  const currentArticle = await getArticleBySlug(currentSlug);
  if (!currentArticle) return [];

  const categoryArticles = await getArticlesByCategory(currentArticle.category);
  return categoryArticles
    .filter(article => article.slug !== currentSlug)
    .slice(0, limit);
}

/**
 * Calculate reading time based on content length
 */
export function calculateReadingTime(content: string): number {
  const wordsPerMinute = 200;
  const wordCount = content.split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
}