import { 
  CalendarIcon, 
  RocketIcon, 
  LightningBoltIcon 
} from "@radix-ui/react-icons";
import { BentoCard, BentoGrid } from "@/components/ui/bento-grid";

const offersData = [
  {
    Icon: CalendarIcon,
    name: "Monthly Management",
    description: "Comprehensive monthly management of your marketing campaigns with ongoing optimization, detailed reporting, and strategic adjustments to maximize ROI.",
    href: "#monthly-management",
    cta: "Learn More",
    background: <img className="absolute -right-20 -top-20 opacity-60" />,
    className: "col-span-1 md:col-span-2 lg:col-span-2", // Full width on mobile, full width on tablet/desktop
  },
  {
    Icon: RocketIcon,
    name: "One-Time Setup",
    description: "Complete setup and launch of your marketing infrastructure including campaign creation, tracking implementation, and initial optimization.",
    href: "#one-time-setup",
    cta: "Get Started",
    background: <img className="absolute -right-20 -top-20 opacity-60" />,
    className: "col-span-1 md:col-span-1 lg:col-span-1", // Half width on tablet/desktop
  },
  {
    Icon: LightningBoltIcon,
    name: "Project Sprint",
    description: "Intensive 2-4 week focused projects to solve specific marketing challenges, implement new strategies, or optimize existing campaigns.",
    href: "#project-sprint",
    cta: "Start Sprint",
    background: <img className="absolute -right-20 -top-20 opacity-60" />,
    className: "col-span-1 md:col-span-1 lg:col-span-1", // Half width on tablet/desktop
  },
];

function OffersSection() {
  return (
    <section className="py-20 px-4 bg-background-alt">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="h2-heading text-libi mb-4">
            Choose Your <span className="text-gango">Growth Path</span>
          </h2>
          <p className="body-text text-libi/70 max-w-2xl mx-auto">
            Whether you need ongoing support, a quick launch, or intensive optimization,
            we have the perfect solution to accelerate your marketing success.
          </p>
        </div>

        {/* Bento Grid */}
        <div className="max-w-6xl mx-auto">
          <BentoGrid className="grid-cols-1 md:grid-cols-2 lg:grid-cols-2 auto-rows-[22rem]">
            {offersData.map((offer) => (
              <BentoCard key={offer.name} {...offer} />
            ))}
          </BentoGrid>
        </div>
      </div>
    </section>
  );
}

export { OffersSection };
