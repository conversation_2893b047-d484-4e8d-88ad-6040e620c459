import { EnhancedNavbar } from "@/components/ui/enhanced-navbar";
import { Footer2Demo } from "@/components/ui/footer2-demo";
import { SEOHead, createServiceSchema } from "@/components/ui/seo-head";
import { TestimonialsDemo } from "@/components/ui/testimonials-demo";
import { ContactForm } from "@/components/ui/contact-form";
import { Button } from "@/components/ui/button";
import { MoveRight, Eye, Users, BarChart3, Zap, Target, TrendingUp, CheckCircle, MousePointer } from "lucide-react";

const PageHeader = ({ title, subtitle }: { title: string; subtitle: string }) => (
  <section className="py-20 px-4">
    <div className="max-w-4xl mx-auto text-center">
      <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
        {title}
      </h1>
      <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
        {subtitle}
      </p>
    </div>
  </section>
);

const PainPointsSection = () => (
  <section className="py-20 px-4 bg-muted/30">
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold text-foreground mb-6">Signs You Need CRO</h2>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
          Are you getting plenty of site visitors but too few sales? Is your bounce rate climbing?
          Our CRO & UX service fixes these problems by turning more visitors into customers.
        </p>
      </div>
      <div className="grid md:grid-cols-3 gap-8">
        <div className="p-8 bg-white rounded-card border text-center">
          <BarChart3 className="h-12 w-12 text-primary mx-auto mb-6" />
          <h3 className="text-xl font-semibold mb-4">Lots of Traffic, Low Sales</h3>
          <p className="text-muted-foreground">
            You're driving visitors to your site but they're not converting into customers or leads at the rate you need.
          </p>
        </div>
        <div className="p-8 bg-white rounded-card border text-center">
          <Eye className="h-12 w-12 text-primary mx-auto mb-6" />
          <h3 className="text-xl font-semibold mb-4">Users Drop Off Before Checkout</h3>
          <p className="text-muted-foreground">
            Visitors abandon their carts or leave forms incomplete, indicating friction in your conversion process.
          </p>
        </div>
        <div className="p-8 bg-white rounded-card border text-center">
          <Users className="h-12 w-12 text-primary mx-auto mb-6" />
          <h3 className="text-xl font-semibold mb-4">Low Engagement with Key Pages</h3>
          <p className="text-muted-foreground">
            Your most important pages aren't performing, and you're not utilizing A/B tests or analytics to optimize them.
          </p>
        </div>
      </div>
    </div>
  </section>
);

const CROApproach = () => (
  <section className="py-20 px-4">
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold text-foreground mb-6">Our CRO Approach</h2>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
          We systematically identify and fix conversion bottlenecks using data-driven methods.
          Understanding user behavior is critical—we follow the data to see where visitors get confused or drop off.
        </p>
      </div>
      <div className="grid md:grid-cols-2 gap-8">
        <div className="bg-white rounded-card p-8 border">
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <Eye className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-semibold">Deep User Behavior Analysis</h3>
          </div>
          <p className="text-muted-foreground">
            We employ tools like heatmaps, session recordings, and analytics audits to understand how real users interact with your site.
            Using advanced analytics and AI-driven qualitative analysis, we pinpoint friction points before making any changes.
          </p>
        </div>

        <div className="bg-white rounded-card p-8 border">
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <Target className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-semibold">Competitive & UX Benchmarking</h3>
          </div>
          <p className="text-muted-foreground">
            We compare your funnel against industry benchmarks and competitors to spot missed opportunities.
            Through competitive analysis, we find conversion tactics your rivals overlook, giving you an edge.
          </p>
        </div>

        <div className="bg-white rounded-card p-8 border">
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <BarChart3 className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-semibold">Rigorous A/B Testing</h3>
          </div>
          <p className="text-muted-foreground">
            We don't rely on guesswork—we run A/B and multivariate tests on headlines, layouts, calls-to-action, forms, and more
            to scientifically increase your conversion rate through structured experiments.
          </p>
        </div>

        <div className="bg-white rounded-card p-8 border">
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <Users className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-semibold">Personalization & UX Improvements</h3>
          </div>
          <p className="text-muted-foreground">
            Beyond generic tests, we deploy personalized experiences where relevant. We tailor content or offers to different audience segments,
            so each visitor sees the experience most likely to convert them.
          </p>
        </div>
      </div>
    </div>
  </section>
);

const FunnelVisionDifference = () => (
  <section className="py-20 px-4 bg-muted/30">
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold text-foreground mb-6">The FunnelVision Difference</h2>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
          We provide clear reports on test results and conversion metrics, so you see what's improving.
          Every win is followed by the next test—our process evolves with your business for continuous growth.
        </p>
      </div>
      <div className="grid md:grid-cols-2 gap-8">
        <div className="bg-white rounded-card p-8 border">
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <BarChart3 className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-semibold">Insight-Led, Not Hunch-Led</h3>
          </div>
          <p className="text-muted-foreground">
            We only make changes backed by data evidence. Our insight-led conversion mapping purposefully maps the user journey
            to ensure every optimization is grounded in real user behavior and performance data.
          </p>
        </div>

        <div className="bg-white rounded-card p-8 border">
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <TrendingUp className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-semibold">Full-Funnel Perspective</h3>
          </div>
          <p className="text-muted-foreground">
            We optimize every step, from the landing page to checkout or sign-up, to squeeze maximum value out of each visitor.
            Our full-funnel experimentation maximizes revenue at every touchpoint.
          </p>
        </div>

        <div className="bg-white rounded-card p-8 border">
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <Zap className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-semibold">Ruthless Elimination of Friction</h3>
          </div>
          <p className="text-muted-foreground">
            We identify and fix everything slowing down your users, whether it's a slow page, confusing form, or unclear copy.
            No detail is too small if it affects conversions.
          </p>
        </div>

        <div className="bg-white rounded-card p-8 border">
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <MousePointer className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-semibold">Continuous Growth Mindset</h3>
          </div>
          <p className="text-muted-foreground">
            Good enough is never enough—we keep iterating to unlock new gains, ensuring your conversion rate keeps climbing over time
            through relentless iteration and growth.
          </p>
        </div>
      </div>
    </div>
  </section>
);





const SocialProofSection = () => (
  <section className="py-20 px-4">
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold text-foreground mb-6">Results & Case Studies</h2>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
          Our clients typically see double-digit percentage increases in conversion rates within the first few months.
          Small UX changes can yield huge ROI when backed by data and testing.
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-8 mb-16">
        <div className="text-center p-8 bg-white rounded-card border">
          <div className="text-4xl font-bold text-primary mb-2">75%</div>
          <div className="text-lg font-semibold mb-2">Add-to-Cart Rate Increase</div>
          <p className="text-muted-foreground text-sm">E-commerce client through CRO optimization</p>
        </div>
        <div className="text-center p-8 bg-white rounded-card border">
          <div className="text-4xl font-bold text-primary mb-2">30%</div>
          <div className="text-lg font-semibold mb-2">Checkout Completion Increase</div>
          <p className="text-muted-foreground text-sm">Reduced friction in the conversion funnel</p>
        </div>
        <div className="text-center p-8 bg-white rounded-card border">
          <div className="text-4xl font-bold text-primary mb-2">2X</div>
          <div className="text-lg font-semibold mb-2">Lead Generation Improvement</div>
          <p className="text-muted-foreground text-sm">Through systematic A/B testing and UX optimization</p>
        </div>
      </div>

      <div className="bg-primary/5 rounded-card p-8 border border-primary/20">
        <div className="flex items-start gap-4">
          <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
            <CheckCircle className="w-6 h-6 text-white" />
          </div>
          <div>
            <blockquote className="text-lg font-medium text-foreground mb-2">
              "FunnelVision helped us turn our website into a conversion machine—they found things we'd never noticed
              and our leads jumped 2x. Their data-driven approach eliminated all the guesswork."
            </blockquote>
            <cite className="text-muted-foreground">— Marketing Director, SaaS Company</cite>
          </div>
        </div>
      </div>
    </div>
  </section>
);

const CroUx = () => {
  const serviceSchema = createServiceSchema(
    "CRO & UX Loop",
    "Boost conversions with data-driven CRO & UX enhancements. Continuous optimization cycles that turn more visitors into customers through systematic testing and user experience improvements."
  );

  return (
    <div className="min-h-screen bg-background">
      <SEOHead
        title="CRO & UX Loop | FunnelVision Services"
        description="Boost conversions with data-driven CRO & UX enhancements. We use analytics, user research, and systematic testing to turn more visitors into customers without needing more ad spend."
        jsonLd={serviceSchema}
      />
      <EnhancedNavbar />
      <div className="pt-16">
        <PageHeader
          title="Boost Conversions with Data-Driven CRO & UX Enhancements"
          subtitle="Driving traffic is only half the battle. Converting traffic into actual sales is what truly grows your business. We use analytics, user research, and testing to systematically lift conversion rates."
        />
        <PainPointsSection />
        <CROApproach />
        <FunnelVisionDifference />
        <SocialProofSection />
        <ContactForm
          title="Ready to Turn Clicks into Customers?"
          subtitle="Don't let interested prospects slip away. Let us identify exactly where your funnel can improve and fix it. Contact us for a free UX audit—we'll pinpoint quick wins to boost your revenue."
        />
        <TestimonialsDemo />
      </div>
      <Footer2Demo />
    </div>
  );
};

export default CroUx;
