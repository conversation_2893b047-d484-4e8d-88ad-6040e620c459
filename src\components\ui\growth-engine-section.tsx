import React from 'react';
import { Search, BarChart3, Zap } from "lucide-react";

function GrowthEngineSection() {
  return (
    <section className="py-20 lg:py-32 bg-background-alt">
      <div className="container mx-auto px-4 md:px-6">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="h2-heading text-libi mb-6">
            One growth engine. <span className="text-gango">Multiple entry points.</span>
          </h2>
          <p className="body-text text-libi/70 max-w-2xl mx-auto">
            Whether you need visibility in AI search, stronger paid performance, or a smoother
            user journey, we'll meet you where the highest-leverage fix is. Every service stands
            alone. Together, <span className="font-medium text-libi">they scale even harder.</span>
          </p>

          {/* Feature Cards - moved from hero section */}
          <div className="flex flex-col md:flex-row gap-4 mt-12 max-w-5xl w-full mx-auto px-4">
            <div className="bg-transparent rounded-2xl p-4 md:p-6 flex items-center gap-3 md:gap-4 flex-1 min-w-0">
              <div className="w-8 h-8 md:w-10 md:h-10 bg-gango/10 rounded-full flex items-center justify-center flex-shrink-0">
                <Search className="w-4 h-4 md:w-5 md:h-5 text-gango" />
              </div>
              <span className="font-redhat font-medium text-sm md:text-base text-libi leading-tight whitespace-nowrap">Win visibility in AI answers</span>
            </div>

            <div className="bg-transparent rounded-2xl p-4 md:p-6 flex items-center gap-3 md:gap-4 flex-1 min-w-0">
              <div className="w-8 h-8 md:w-10 md:h-10 bg-gango/10 rounded-full flex items-center justify-center flex-shrink-0">
                <BarChart3 className="w-4 h-4 md:w-5 md:h-5 text-gango" />
              </div>
              <span className="font-redhat font-medium text-sm md:text-base text-libi leading-tight whitespace-nowrap">Scale with search that compounds</span>
            </div>

            <div className="bg-transparent rounded-2xl p-4 md:p-6 flex items-center gap-3 md:gap-4 flex-1 min-w-0">
              <div className="w-8 h-8 md:w-10 md:h-10 bg-gango/10 rounded-full flex items-center justify-center flex-shrink-0">
                <Zap className="w-4 h-4 md:w-5 md:h-5 text-gango" />
              </div>
              <span className="font-redhat font-medium text-sm md:text-base text-libi leading-tight whitespace-nowrap">Convert demand with CRO & UX</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export { GrowthEngineSection };
