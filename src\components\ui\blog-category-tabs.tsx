import { BlogCategory, BLOG_CATEGORIES } from '@/types/blog';
import { cn } from '@/lib/utils';

interface BlogCategoryTabsProps {
  activeCategory: 'all' | BlogCategory;
  onCategoryChange: (category: 'all' | BlogCategory) => void;
}

export function BlogCategoryTabs({ activeCategory, onCategoryChange }: BlogCategoryTabsProps) {
  const allCategories = [
    { id: 'all' as const, name: 'All Articles', slug: 'all' },
    ...BLOG_CATEGORIES
  ];

  return (
    <div className="w-full mb-12">
      {/* Desktop Tabs */}
      <div className="hidden md:flex flex-wrap gap-3 justify-center">
        {allCategories.map((category) => (
          <button
            key={category.id}
            onClick={() => onCategoryChange(category.id)}
            className={cn(
              "px-6 py-3 rounded-full font-redhat font-medium text-base transition-all duration-200",
              "hover:bg-sulu/20 hover:text-libi",
              activeCategory === category.id
                ? "bg-bango shadow-md"
                : "bg-white text-libi border border-lungu"
            )}
            style={activeCategory === category.id ? { color: '#F8FAFC' } : {}}
          >
            {category.name}
          </button>
        ))}
      </div>

      {/* Mobile Dropdown */}
      <div className="md:hidden">
        <select
          value={activeCategory}
          onChange={(e) => onCategoryChange(e.target.value as 'all' | BlogCategory)}
          className="w-full px-4 py-3 rounded-lg border border-lungu bg-white text-libi font-redhat font-medium focus:outline-none focus:ring-2 focus:ring-bango focus:border-bango"
        >
          {allCategories.map((category) => (
            <option key={category.id} value={category.id}>
              {category.name}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
}
