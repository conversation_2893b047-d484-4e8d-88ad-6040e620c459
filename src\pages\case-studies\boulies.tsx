import { EnhancedNavbar } from "@/components/ui/enhanced-navbar";
import { Footer2Demo } from "@/components/ui/footer2-demo";
import { SEOHead, createCaseStudySchema } from "@/components/ui/seo-head";
import { CTADemo } from "@/components/ui/call-to-action-demo";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, TrendingDown, Target, BarChart3 } from "lucide-react";

const CaseStudyHeader = () => (
  <section className="py-20 px-4">
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <Button variant="ghost" className="font-redhat font-medium text-base md:text-lg tracking-tight gap-2 mb-6" onClick={() => window.history.back()}>
          <ArrowLeft className="h-4 w-4" />
          Back to Case Studies
        </Button>
      </div>
      
      <div className="flex items-center gap-4 mb-8">
        <img 
          src="/images/clients/10.svg" 
          alt="Boulies" 
          className="h-12 w-auto"
        />
        <div>
          <h1 className="font-redhat font-medium leading-tight md:leading-[0.95] lg:leading-[0.9] tracking-tight text-[clamp(2.2rem,7vw,3.5rem)] md:text-[72px] lg:text-[88px] xl:text-[96px] text-foreground">
            Boulies Case Study
          </h1>
          <p className="font-redhat text-xl md:text-2xl text-muted-foreground mt-2">
            How we cut wasted ad spend by 52.6% while increasing qualified leads
          </p>
        </div>
      </div>
      
      <div className="aspect-video bg-muted rounded-card overflow-hidden mb-8">
        <img 
          src="/images/case-studies/case-1.webp" 
          alt="Boulies Case Study" 
          className="w-full h-full object-cover"
        />
      </div>
    </div>
  </section>
);

const Challenge = () => (
  <section className="py-20 px-4 bg-muted/10">
    <div className="max-w-4xl mx-auto">
      <h2 className="font-redhat font-medium leading-snug tracking-tight text-2xl md:text-3xl lg:text-4xl mb-8">The Challenge</h2>
      <div className="prose prose-lg max-w-none">
        <p className="text-muted-foreground leading-relaxed mb-6">
          Boulies, a premium outdoor furniture brand, was struggling with inefficient paid search campaigns 
          that were burning through budget without delivering quality leads. Their cost per acquisition was 
          climbing while conversion rates were declining.
        </p>
        
        <div className="grid md:grid-cols-3 gap-6 my-8">
          <div className="p-6 bg-background rounded-card border">
            <TrendingDown className="h-8 w-8 text-red-500 mb-4" />
            <h3 className="font-redhat font-medium text-base md:text-lg mb-2">High Waste</h3>
            <p className="font-redhat text-sm text-muted-foreground">52.6% of ad spend was going to unqualified clicks</p>
          </div>
          <div className="p-6 bg-background rounded-card border">
            <Target className="h-8 w-8 text-orange-500 mb-4" />
            <h3 className="font-redhat font-medium text-base md:text-lg mb-2">Poor Targeting</h3>
            <p className="font-redhat text-sm text-muted-foreground">Campaigns weren't reaching high-intent customers</p>
          </div>
          <div className="p-6 bg-background rounded-card border">
            <BarChart3 className="h-8 w-8 text-yellow-500 mb-4" />
            <h3 className="font-redhat font-medium text-base md:text-lg mb-2">Low ROI</h3>
            <p className="font-redhat text-sm text-muted-foreground">Return on ad spend was below industry benchmarks</p>
          </div>
        </div>
      </div>
    </div>
  </section>
);

const Solution = () => (
  <section className="py-20 px-4">
    <div className="max-w-4xl mx-auto">
      <h2 className="font-redhat font-medium leading-snug tracking-tight text-2xl md:text-3xl lg:text-4xl mb-8">Our Solution</h2>
      <div className="prose prose-lg max-w-none">
        <p className="text-muted-foreground leading-relaxed mb-6">
          We implemented a comprehensive paid search orchestration strategy that focused on intent-based 
          targeting, negative keyword optimization, and conversion-focused landing page improvements.
        </p>
        
        <div className="space-y-6">
          <div className="border-l-4 border-primary pl-6">
            <h3 className="font-redhat font-medium leading-normal tracking-tight text-xl md:text-2xl mb-2">1. Audience Refinement</h3>
            <p className="font-redhat text-base md:text-lg text-muted-foreground">
              We analyzed customer data to identify high-value audience segments and created targeted campaigns
              for each segment with specific messaging and offers.
            </p>
          </div>

          <div className="border-l-4 border-primary pl-6">
            <h3 className="font-redhat font-medium leading-normal tracking-tight text-xl md:text-2xl mb-2">2. Keyword Optimization</h3>
            <p className="font-redhat text-base md:text-lg text-muted-foreground">
              We implemented extensive negative keyword lists and refined match types to eliminate wasteful
              clicks while capturing high-intent searches.
            </p>
          </div>

          <div className="border-l-4 border-primary pl-6">
            <h3 className="font-redhat font-medium leading-normal tracking-tight text-xl md:text-2xl mb-2">3. Landing Page Alignment</h3>
            <p className="font-redhat text-base md:text-lg text-muted-foreground">
              We created campaign-specific landing pages that matched search intent and improved the
              user experience from click to conversion.
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>
);

const Results = () => (
  <section className="py-20 px-4 bg-primary/5">
    <div className="max-w-4xl mx-auto">
      <h2 className="font-redhat font-medium leading-snug tracking-tight text-2xl md:text-3xl lg:text-4xl mb-8 text-center">The Results</h2>
      <div className="grid md:grid-cols-3 gap-8 mb-8">
        <div className="text-center">
          <div className="text-5xl font-bold text-primary mb-2">52.6%</div>
          <p className="text-muted-foreground">Reduction in wasted ad spend</p>
        </div>
        <div className="text-center">
          <div className="text-5xl font-bold text-primary mb-2">127%</div>
          <p className="text-muted-foreground">Increase in qualified leads</p>
        </div>
        <div className="text-center">
          <div className="text-5xl font-bold text-primary mb-2">3.4x</div>
          <p className="text-muted-foreground">Improvement in ROAS</p>
        </div>
      </div>
      
      <div className="bg-background rounded-lg p-8 border">
        <blockquote className="text-lg italic text-center">
          "The results were immediate and impressive. FunnelVision didn't just reduce our wasted spend—they 
          helped us understand our customers better and reach them more effectively."
        </blockquote>
        <div className="text-center mt-4">
          <p className="font-semibold">Giselle</p>
          <p className="text-sm text-muted-foreground">Marketing Director, Boulies</p>
        </div>
      </div>
    </div>
  </section>
);

const Boulies = () => {
  const caseStudySchema = createCaseStudySchema(
    "Boulies Case Study: Cut wasted ad spend by 52.6%",
    "Boulies",
    [
      "52.6% reduction in wasted ad spend",
      "127% increase in qualified leads",
      "3.4x improvement in ROAS"
    ]
  );

  return (
    <div className="min-h-screen bg-background">
      <SEOHead
        title="Case Study: Boulies | FunnelVision"
        description="How we cut Boulies' wasted ad spend by 52.6% while increasing qualified leads by 127% through strategic paid search orchestration."
        jsonLd={caseStudySchema}
      />
      <EnhancedNavbar />
      <div className="pt-16">
        <CaseStudyHeader />
        <Challenge />
        <Solution />
        <Results />
        <CTADemo />
      </div>
      <Footer2Demo />
    </div>
  );
};

export default Boulies;
