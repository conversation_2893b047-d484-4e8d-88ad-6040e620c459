import { BlogArticle } from '@/types/blog';
import { Button } from '@/components/ui/button';
import { ExternalLink } from 'lucide-react';

interface AuthorBioProps {
  author: BlogArticle['author'];
}

export function AuthorBio({ author }: AuthorBioProps) {
  return (
    <div className="bg-sulu rounded-2xl p-6 md:p-8">
      <div className="flex flex-col md:flex-row gap-6 items-start">
        {/* Author Avatar */}
        <div className="flex-shrink-0">
          <div className="w-20 h-20 md:w-24 md:h-24 rounded-full overflow-hidden bg-white shadow-md">
            <img
              src={author.avatar || '/placeholder.svg'}
              alt={author.name}
              className="w-full h-full object-cover"
              onError={(e) => {
                // Fallback to placeholder if image fails to load
                e.currentTarget.src = '/placeholder.svg';
              }}
            />
          </div>
        </div>

        {/* Author Info */}
        <div className="flex-1">
          <div className="mb-4">
            <h3 className="font-redhat font-medium text-xl text-libi mb-2">
              About {author.name}
            </h3>
            {author.bio && (
              <p className="text-libi/80 text-base leading-relaxed">
                {author.bio}
              </p>
            )}
          </div>

          {/* LinkedIn Link */}
          {author.linkedinUrl && (
            <Button
              onClick={() => window.open(author.linkedinUrl, '_blank')}
              className="font-redhat font-medium bg-svibi hover:bg-svibi/90 text-white border-0 rounded-full gap-2"
              size="sm"
            >
              Connect on LinkedIn
              <ExternalLink className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
