import { EnhancedNavbar } from "@/components/ui/enhanced-navbar";
import { Footer2Demo } from "@/components/ui/footer2-demo";
import { SEOHead, createServiceSchema } from "@/components/ui/seo-head";
import { TestimonialsDemo } from "@/components/ui/testimonials-demo";
import { ContactForm } from "@/components/ui/contact-form";
import { Button } from "@/components/ui/button";
import { MoveRight, Code, Smartphone, Zap, Shield, Users, BarChart3, Settings, CheckCircle } from "lucide-react";

const PageHeader = ({ title, subtitle }: { title: string; subtitle: string }) => (
  <section className="py-20 px-4">
    <div className="max-w-4xl mx-auto text-center">
      <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
        {title}
      </h1>
      <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
        {subtitle}
      </p>
    </div>
  </section>
);

const WhyItMatters = () => (
  <section className="py-20 px-4 bg-muted/30">
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold text-foreground mb-6">Why Great Web Development Matters</h2>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
          Even a beautiful site fails if it's slow or confusing. Users form an impression in 0.05 seconds, and a well-designed site can double conversion rates.
          We make sure that never happens by building websites that don't just look good—they generate leads and sales.
        </p>
      </div>
    </div>
  </section>
);

const KeyElements = () => (
  <section className="py-20 px-4">
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold text-foreground mb-6">Key Website Elements - What We Do</h2>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
          We solve the crucial components of a high-converting website, focusing on the pillars that drive business results.
        </p>
      </div>
      <div className="grid md:grid-cols-2 gap-8">
        <div className="p-8 bg-white rounded-card border">
          <Users className="h-10 w-10 text-primary mb-6" />
          <h3 className="text-xl font-semibold mb-4">User Experience & Design</h3>
          <p className="text-muted-foreground">
            Intuitive navigation, mobile-first design, and user-centric layouts so visitors find what they need and take action effortlessly.
            Seamless page flows and clear CTAs guide users toward converting.
          </p>
        </div>
        <div className="p-8 bg-white rounded-card border">
          <Zap className="h-10 w-10 text-primary mb-6" />
          <h3 className="text-xl font-semibold mb-4">Technical Performance</h3>
          <p className="text-muted-foreground">
            Lightning-fast loading, SEO-friendly code, and robust security. We use modern frameworks and optimize Core Web Vitals
            to improve both UX and search ranking with fast loading speeds and scalable architecture.
          </p>
        </div>
        <div className="p-8 bg-white rounded-card border">
          <BarChart3 className="h-10 w-10 text-primary mb-6" />
          <h3 className="text-xl font-semibold mb-4">Conversion Optimization Features</h3>
          <p className="text-muted-foreground">
            Strategic placement of forms, compelling landing pages, A/B tested layouts. Every site is structured to turn visitors into leads
            through persuasive content blocks, trust indicators, and clear conversion paths.
          </p>
        </div>
        <div className="p-8 bg-white rounded-card border">
          <Settings className="h-10 w-10 text-primary mb-6" />
          <h3 className="text-xl font-semibold mb-4">Integration & Scalability</h3>
          <p className="text-muted-foreground">
            Integration with marketing tools (CRM, analytics, email) and easy scalability. We connect websites with all necessary
            third-party platforms for a holistic growth ecosystem that scales with your business.
          </p>
        </div>
      </div>
    </div>
  </section>
);

const DevelopmentApproach = () => (
  <section className="py-20 px-4 bg-muted/30">
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold text-foreground mb-6">How We Build Websites That Drive Growth</h2>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
          Our development process is designed to create websites that don't just exist, but sell. We combine strategic design,
          UX optimization, and technical excellence to build conversion-focused sites.
        </p>
      </div>
      <div className="grid md:grid-cols-2 gap-8">
        <div className="bg-white rounded-card p-8 border">
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <Users className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-semibold">Discovery & Strategy</h3>
          </div>
          <p className="text-muted-foreground">
            We begin by deeply researching your market, users, and goals. We analyze your audience's needs and study competitor sites
            to identify gaps—ensuring our design strategy aligns with what customers want and what your business needs.
          </p>
        </div>

        <div className="bg-white rounded-card p-8 border">
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <Code className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-semibold">UX Design & Customer Journey</h3>
          </div>
          <p className="text-muted-foreground">
            We map out the optimal user journey before writing a line of code. We craft wireframes and prototypes focusing on intuitive flow
            from landing to conversion, designing each page with a purpose—guiding users from first click to contact form with minimal friction.
          </p>
        </div>

        <div className="bg-white rounded-card p-8 border">
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <Zap className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-semibold">Development & Performance</h3>
          </div>
          <p className="text-muted-foreground">
            During development, we focus on clean, efficient code and modern best practices. Our developers build with performance in mind—
            optimizing for fast load times, mobile responsiveness, and robust security with technically excellent, lightning-fast loading sites.
          </p>
        </div>

        <div className="bg-white rounded-card p-8 border">
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <BarChart3 className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-semibold">Testing, Launch & Iteration</h3>
          </div>
          <p className="text-muted-foreground">
            Prior to launch we rigorously test (QA, A/B testing elements, etc.), and post-launch we monitor and improve. We don't just hand over the site—
            we monitor real user behavior via analytics and heatmaps, and iterate to continually boost performance.
          </p>
        </div>
      </div>
    </div>
  </section>
);





const SocialProofSection = () => (
  <section className="py-20 px-4">
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold text-foreground mb-6">Websites That Deliver Results</h2>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
          Our team has delivered successful websites that increased conversion rates by 2X on average.
          We build sites that don't just look good—they generate leads and sales.
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-8 mb-16">
        <div className="text-center p-8 bg-white rounded-card border">
          <div className="text-4xl font-bold text-primary mb-2">2X</div>
          <div className="text-lg font-semibold mb-2">Average Conversion Increase</div>
          <p className="text-muted-foreground text-sm">Through expert development and UX optimization</p>
        </div>
        <div className="text-center p-8 bg-white rounded-card border">
          <div className="text-4xl font-bold text-primary mb-2">&lt;2s</div>
          <div className="text-lg font-semibold mb-2">Page Load Time</div>
          <p className="text-muted-foreground text-sm">Lightning-fast performance on all devices</p>
        </div>
        <div className="text-center p-8 bg-white rounded-card border">
          <div className="text-4xl font-bold text-primary mb-2">50%</div>
          <div className="text-lg font-semibold mb-2">Lead Generation Increase</div>
          <p className="text-muted-foreground text-sm">From conversion-focused design and optimization</p>
        </div>
      </div>

      <div className="bg-primary/5 rounded-card p-8 border border-primary/20">
        <div className="flex items-start gap-4">
          <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
            <CheckCircle className="w-6 h-6 text-white" />
          </div>
          <div>
            <blockquote className="text-lg font-medium text-foreground mb-2">
              "Since FunnelVision rebuilt our website, our page load time is under 2s and lead generation jumped 50%.
              The site doesn't just look professional—it actually converts visitors into customers."
            </blockquote>
            <cite className="text-muted-foreground">— CEO, E-commerce Company</cite>
          </div>
        </div>
      </div>
    </div>
  </section>
);

const WebDevelopment = () => {
  const serviceSchema = createServiceSchema(
    "Web Development",
    "High-performance websites engineered to convert. We combine strategic design, UX optimization, and technical excellence to create sites that generate leads and sales."
  );

  return (
    <div className="min-h-screen bg-background">
      <SEOHead
        title="Web Development | FunnelVision Services"
        description="High-performance websites engineered to convert. We combine strategic design, UX optimization, and technical excellence to create sites that generate leads and sales, not just look good."
        jsonLd={serviceSchema}
      />
      <EnhancedNavbar />
      <div className="pt-16">
        <PageHeader
          title="High-Performance Websites Engineered to Convert"
          subtitle="We build websites that aren't just pretty brochures, but powerful sales engines. Our team combines strategic design, UX optimization, and technical excellence to create sites that generate leads and sales."
        />
        <WhyItMatters />
        <KeyElements />
        <DevelopmentApproach />
        <SocialProofSection />
        <ContactForm
          title="Ready for a Website That Doesn't Just Exist, But Sells?"
          subtitle="Let's turn your website into a 24/7 sales machine. Get a free site audit to see how we can double your conversion rate."
        />
        <TestimonialsDemo />
      </div>
      <Footer2Demo />
    </div>
  );
};

export default WebDevelopment;
