import { InteractiveTabs } from "@/components/ui/interactive-tabs";

const tabsData = [
  {
    id: "discover",
    label: "Discover",
    title: "Get discovered in ChatGPT, Perplexity, and Google's AI Overviews",
    description: "Win buyer attention before competitors show up with Answer Engine Optimization",
    benefits: [
      "Win buyer attention before competitors show up",
      "Get high-intent traffic without high ad costs",
      "Future-proof your funnel against changing search"
    ],
    image: "/images/growth engine/Answer Engine Optimization.mp4",
    ctaText: "Be first in AI answers",
    ctaAction: () => window.location.href = "/services/ai-discovery"
  },
  {
    id: "demand",
    label: "Demand",
    title: "Drive qualified traffic through Google & YouTube Ads",
    description: "Strategic paid search campaigns that turn customer intent into profitable conversions at scale",
    benefits: [
      "Acquire customers with high-intent paid traffic",
      "Improve CAC with compounding campaign structure",
      "Scale spend without scaling wasted budget"
    ],
    image: "/images/growth engine/Google Ads.mp4",
    ctaText: "Drive qualified demand",
    ctaAction: () => window.location.href = "/services/paid-search"
  },
  {
    id: "authority",
    label: "Authority",
    title: "Build lasting visibility with SEO that compounds",
    description: "Establish your brand as the go-to solution with strategic content and positioning",
    benefits: [
      "Grow trust and traffic that compounds over time",
      "Win visibility in organic and AI-driven results",
      "Strengthen relevance across search and surfaces"
    ],
    image: "/images/growth engine/Search Engine Optimization.mp4",
    ctaText: "Build lasting authority",
    ctaAction: () => window.location.href = "/services/seo-content"
  },
  {
    id: "convert",
    label: "Convert",
    title: "Optimize your funnel to convert more visitors",
    description: "Data-driven optimization that removes hidden friction and maximizes conversion rates",
    benefits: [
      "Remove hidden friction killing conversions",
      "Improve flow across mobile, PDPs, and forms",
      "Get more revenue from your existing traffic"
    ],
    image: "/images/growth engine/Conversion Rate Optimization-UX.mp4",
    ctaText: "Optimize conversions",
    ctaAction: () => window.location.href = "/services/cro-ux"
  }
];

function InteractiveTabsDemo() {
  return (
    <div className="w-full">
      <InteractiveTabs tabs={tabsData} />
    </div>
  );
}

export { InteractiveTabsDemo };
