@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 40 8% 93%;
    --foreground: 225 8% 14%;

    --card: 0 0% 100%;
    --card-foreground: 225 8% 14%;

    --popover: 0 0% 100%;
    --popover-foreground: 225 8% 14%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 36 17% 92%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 36 17% 92%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 36 17% 92%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --heading: 0 0% 0%;
    --body: 225 8% 14%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-redhat;
    letter-spacing: normal;
    font-weight: 400;
  }

  /* Global heading weights + letter-spacing */
  h1 {
    font-weight: 700 !important;
    letter-spacing: -1px !important;
  }
  h2, h3, h4, h5, h6 {
    font-weight: 500 !important;
    letter-spacing: -0.4px !important;
  }
}

@layer components {
  /* H1 Typography System */
  .h1-heading {
    @apply font-redhat;
    /* Mobile: 35.2px, line-height 44px, font-weight 700, letter-spacing -1px */
    font-size: 35.2px;
    line-height: 44px;
    font-weight: 700;
    letter-spacing: -1px;
  }

  /* Tablet H1 */
  @media (min-width: 768px) {
    .h1-heading {
      /* Tablet: 72px, line-height 68.4px, font-weight 700, letter-spacing -1px */
      font-size: 72px;
      line-height: 68.4px;
      font-weight: 700;
      letter-spacing: -1px;
    }
  }

  /* Desktop H1 */
  @media (min-width: 1024px) {
    .h1-heading {
      /* Desktop: 96px, line-height 86.4px, font-weight 700, letter-spacing -1px */
      font-size: 96px;
      line-height: 86.4px;
      font-weight: 700;
      letter-spacing: -1px;
    }
  }

  /* Description Text Under H1 */
  .h1-description {
    @apply font-redhat;
    /* Mobile: 20px, line-height 28px, font-weight 400 */
    font-size: 20px;
    line-height: 28px;
    font-weight: 400;
    letter-spacing: normal;
  }

  /* Tablet Description */
  @media (min-width: 768px) {
    .h1-description {
      /* Tablet: 24px, line-height 32px, font-weight 400 */
      font-size: 24px;
      line-height: 32px;
      font-weight: 400;
      letter-spacing: normal;
    }
  }

  /* CTA Button Typography */
  .cta-button {
    @apply font-redhat;
    /* Mobile: height 48px, font 16px, line-height 24px, font-weight 500, padding 24px */
    height: 48px;
    font-size: 16px;
    line-height: 24px;
    font-weight: 500;
    letter-spacing: normal;
    padding-left: 24px;
    padding-right: 24px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
  }

  /* Tablet & Desktop CTA */
  @media (min-width: 768px) {
    .cta-button {
      /* Tablet/Desktop: height 56px, font 16px, line-height 24px, font-weight 500, padding 24px */
      height: 56px;
      font-size: 16px;
      line-height: 24px;
      font-weight: 500;
      letter-spacing: normal;
      padding-left: 24px;
      padding-right: 24px;
    }
  }

  /* CTA Arrow Icon */
  .cta-arrow {
    width: 16px;
    height: 16px;
    transform: rotate(45deg);
    transition: transform 0.2s ease;
  }

  .cta-button:hover .cta-arrow {
    transform: rotate(45deg) translate(2px, -2px);
  }

  /* H2 Typography System */
  h2.h2-heading {
    font-family: 'Space Grotesk', ui-sans-serif, system-ui, sans-serif;
    font-size: 26px;
    line-height: 33px;
    font-weight: 500;
    letter-spacing: -0.4px;
  }

  /* Tablet & Desktop H2 */
  @media (min-width: 768px) {
    h2.h2-heading {
      font-size: 48px;
      line-height: 57.6px;
      font-weight: 500;
      letter-spacing: -0.4px;
    }
  }

  /* H3 Typography */
  h3.font-redhat {
    font-family: 'Space Grotesk', ui-sans-serif, system-ui, sans-serif;
    font-size: 24px;
    line-height: 32px;
    font-weight: 500;
    letter-spacing: -0.4px;
  }

  @media (min-width: 768px) {
    h3.font-redhat {
      font-size: 24px;
      line-height: 32px;
      font-weight: 500;
      letter-spacing: -0.4px;
    }
  }

  /* H4 Typography */
  h4.font-redhat {
    font-family: 'Space Grotesk', ui-sans-serif, system-ui, sans-serif;
    font-size: 20px;
    line-height: 26px;
    font-weight: 500;
    letter-spacing: -0.4px;
  }

  @media (min-width: 768px) {
    h4.font-redhat {
      font-size: 24px;
      line-height: 29px;
      font-weight: 500;
      letter-spacing: -0.4px;
    }
  }

  /* P Typography */
  p.font-redhat {
    font-family: 'Space Grotesk', ui-sans-serif, system-ui, sans-serif;
    font-size: 16px;
    line-height: 27px;
    font-weight: 400;
    letter-spacing: normal;
  }

  @media (min-width: 768px) {
    p.font-redhat {
      font-size: 18px;
      line-height: 29.25px;
      font-weight: 400;
      letter-spacing: normal;
    }
  }

  /* Span Typography */
  span.font-redhat {
    font-family: 'Space Grotesk', ui-sans-serif, system-ui, sans-serif;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    letter-spacing: normal;
  }

  @media (min-width: 768px) {
    span.font-redhat {
      font-size: 16px;
      line-height: 24px;
      font-weight: 400;
      letter-spacing: normal;
    }
  }

  /* Accordion Closed Titles */
  p.accordion-closed {
    font-family: 'Space Grotesk', ui-sans-serif, system-ui, sans-serif;
    font-size: 16px;
    line-height: 27px;
    font-weight: 400;
    letter-spacing: normal;
  }

  @media (min-width: 768px) {
    p.accordion-closed {
      font-size: 24px;
      line-height: 40px;
      font-weight: 400;
      letter-spacing: normal;
    }
  }

  /* Accordion Closed Titles - Span Fallback */
  span.accordion-closed {
    font-family: 'Space Grotesk', ui-sans-serif, system-ui, sans-serif;
    font-size: 16px;
    line-height: 27px;
    font-weight: 400;
    letter-spacing: normal;
  }

  @media (min-width: 768px) {
    span.accordion-closed {
      font-size: 24px;
      line-height: 40px;
      font-weight: 400;
      letter-spacing: normal;
    }
  }

  /* Ensure typography plugin (prose) headings follow the spec */
  .prose h1 { font-weight: 700; letter-spacing: -1px; }
  .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 { font-weight: 500; letter-spacing: -0.4px; }

  /* Body Text Typography */
  .body-text {
    @apply font-redhat;
    /* Mobile: 16px, line-height 24px, font-weight 400 */
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    letter-spacing: normal;
  }

  /* Tablet & Desktop Body Text */
  @media (min-width: 768px) {
    .body-text {
      /* Tablet/Desktop: 20px, line-height 28px, font-weight 400 */
      font-size: 20px;
      line-height: 28px;
      font-weight: 400;
      letter-spacing: normal;
    }
  }
}

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Hide scrollbar for testimonial carousel */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
