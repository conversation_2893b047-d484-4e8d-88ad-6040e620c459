import { ChevronLeft, ChevronRight } from "lucide-react";
import { useState, useRef, useEffect } from "react";

import { Badge } from "@/components/ui/badge";
import { VideoPlayer } from "@/components/ui/video-thumbnail-player";

interface Feature108Props {
  badge?: string;
  heading?: string;
  description?: string;
}

const Feature108 = ({
  badge = "shadcnblocks.com",
  heading = "A Collection of Components Built With Shadcn & Tailwind",
  description = "Join us to build flawless web solutions."
}: Feature108Props) => {

  return (
    <section className="py-32" style={{ backgroundColor: '#1a1a1a', borderRadius: '24px' }}>
      <div className="sm:max-w-none 2xl:max-w-[1728px] mx-auto">
        {/* Services container - transparent background */}
        <div className="bg-transparent p-6 md:p-8 lg:p-12 mx-auto w-full min-h-[750px] lg:min-h-[750px] md:min-h-[600px] sm:min-h-[500px]">
          {/* Header section inside services container */}
          <div className="flex flex-col items-center gap-4 text-center mb-16">
            {badge && <Badge variant="outline">{badge}</Badge>}
            <h2 className="h2-heading max-w-2xl text-gango">
              {heading}
            </h2>
            <p className="body-text text-gango max-w-2xl mx-auto">{description}</p>
          </div>

          {/* Video Component */}
          <div className="mb-16 w-full max-w-[1280px] mx-auto">
            <VideoPlayer
              thumbnailUrl="https://images.unsplash.com/photo-1593642532454-e138e28a63f4?q=80&w=2069&auto=format&fit=crop"
              videoUrl="https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1"
              title="Building the Future"
              description="A look into modern architecture and design."
              className="rounded-xl"
            />
          </div>

          {/* Testimonial Carousel */}
          <TestimonialCarousel />
        </div>
      </div>
    </section>
  );
};

// Testimonial data
const testimonialData = [
  {
    text: "Yananai has a sixth sense for finding growth opportunities. He spotted gaps in our strategy no one else saw and helped us scale BEFORE increasing our budget.",
    name: "Josh Hill",
    title: "Co-Founder - Dirt",
    image: "/images/testimonials/t2.webp"
  },
  {
    text: "Every recommendation felt tailored to our business, and every week we saw measurable improvements.",
    name: "Giselle",
    title: "Marketing Director - Boulies",
    image: "/images/testimonials/t3.webp"
  },
  {
    text: "Excellent experience with Yananai and the whole team. Insightful, helpful and patient group of people.",
    name: "David Fraser",
    title: "CEO & Founder - Bunkie Life",
    image: "/images/testimonials/t1.webp"
  },
  {
    text: "FunnelVision are not just an extension of your marketing team as a paid search agency, but they are collaborative, proactive partners.",
    name: "Ayanda",
    title: "Head of Demand Generation",
    image: "/images/testimonials/t7.webp"
  },
  {
    text: "Our CAC dropped overnight, and our revenue soared. There is now more synergy between marketing and sales efforts.",
    name: "Peeter Kuum",
    title: "Sales Director - Wermo",
    image: "/images/testimonials/t8.webp"
  }
];

// TestimonialCarousel Component
const TestimonialCarousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const carouselRef = useRef<HTMLDivElement>(null);
  const intervalRef = useRef<number | null>(null);

  const scrollToIndex = (index: number) => {
    if (carouselRef.current) {
      const cardWidth = 400 + 24; // card width + 24px gap
      carouselRef.current.scrollTo({
        left: index * cardWidth,
        behavior: 'smooth'
      });
      setCurrentIndex(index);
    }
  };

  const nextSlide = () => {
    const nextIndex = (currentIndex + 1) % testimonialData.length;
    scrollToIndex(nextIndex);
  };

  const prevSlide = () => {
    const prevIndex = currentIndex === 0 ? testimonialData.length - 1 : currentIndex - 1;
    scrollToIndex(prevIndex);
  };

  // Auto-advance with infinite loop
  useEffect(() => {
    const stepMs = 3500; // timing between slides
    const cardWidth = 400 + 24; // width + gap

    const id = window.setInterval(() => {
      if (carouselRef.current) {
        const container = carouselRef.current;
        const maxScroll = container.scrollWidth - container.clientWidth;
        const currentScroll = container.scrollLeft;

        // If we're near the end, smoothly scroll to the beginning
        if (currentScroll >= maxScroll - cardWidth) {
          container.scrollTo({ left: 0, behavior: 'smooth' });
          setCurrentIndex(0);
        } else {
          // Normal scroll to next item
          const nextScroll = currentScroll + cardWidth;
          container.scrollTo({ left: nextScroll, behavior: 'smooth' });
          setCurrentIndex((prev) => (prev + 1) % testimonialData.length);
        }
      }
    }, stepMs);

    return () => window.clearInterval(id);
  }, []);

  return (
    <div className="relative group">
      {/* Carousel Container */}
      <div
        ref={carouselRef}
        className="flex gap-6 md:gap-6 lg:gap-6 overflow-x-auto scrollbar-hide scroll-smooth pb-4"
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        onMouseEnter={() => { /* do not pause on hover */ }}
        onMouseLeave={() => { /* do not pause on hover */ }}
      >
        {/* Render testimonials twice for infinite loop effect */}
        {[...testimonialData, ...testimonialData].map((testimonial, index) => (
          <div
            key={index}
            className="flex-shrink-0 bg-bango rounded-lg p-6 md:p-8 lg:p-12"
            style={{
              width: 'min(400px, 82vw)',
              height: '370.02px',
              maxWidth: '400px',
              color: '#F8FAFC'
            }}
          >
            {/* Testimonial Content */}
            <div className="flex flex-col h-full">
              {/* Description */}
              <div className="flex-1 mb-6 md:mb-8 max-w-[304px]">
                <p className="text-lg leading-[25.2px]">
                  "{testimonial.text}"
                </p>
              </div>

              {/* Client Details */}
              <div className="flex items-center gap-4">
                <img
                  src={testimonial.image}
                  alt={testimonial.name}
                  className="w-12 h-12 rounded-full object-cover"
                />
                <div>
                  <div className="text-[22px] font-medium leading-[30.8px] text-white">
                    {testimonial.name}
                  </div>
                  <div className="text-[12px] leading-[16.8px] text-white">
                    {testimonial.title}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>



      {/* Dots Indicator */}
      <div className="flex justify-center gap-2 mt-6">
        {testimonialData.map((_, index) => (
          <button
            key={index}
            onClick={() => scrollToIndex(index)}
            className={`w-2 h-2 rounded-full transition-colors ${
              index === currentIndex ? 'bg-bango' : 'bg-bango/30'
            }`}
          />
        ))}
      </div>
    </div>
  );
};

export { Feature108 };
